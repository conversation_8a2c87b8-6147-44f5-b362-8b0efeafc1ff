
        async function toggleCableDetails(element, cableId, cableData) {
            const cableResult = element.closest('.cable-result');
            const detailsDiv = cableResult.querySelector('.cable-details');
            const landingPoints = cableResult.querySelector('.cable-landing-points');

            // First collapse all other cables
            document.querySelectorAll('.cable-result').forEach(result => {
                if (result !== cableResult) {
                    result.classList.remove('expanded');
                    result.querySelector('.cable-details').style.display = 'none';
                    const lp = result.querySelector('.cable-landing-points');
                    if (lp) lp.style.display = 'none';
                }
            });

            // Toggle current cable
            const isExpanded = cableResult.classList.contains('expanded');
            if (!isExpanded) {
                cableResult.classList.add('expanded');
                detailsDiv.style.display = 'block';
                if (landingPoints) landingPoints.style.display = 'block';

                // 🎯 SMART FILTERING: Use selectIndividualCable which handles context-aware filtering
                console.log(`🔍 Search result expanded: Applying smart filtering for cable ${cableId}`);
                selectIndividualCable(cableId, cableData);
            } else {
                cableResult.classList.remove('expanded');
                detailsDiv.style.display = 'none';
                if (landingPoints) landingPoints.style.display = 'none';

                // Clear landing point filter when collapsing
                clearLandingPointFilter();
            }
        }

        const map = L.map('map', {
            worldCopyJump: true,  // Enable world wrapping for continuous view
            maxBounds: [[-90, -360], [90, 360]], // Extended bounds to allow wrapping
            maxBoundsViscosity: 0.8,
            minZoom: 2,
            maxZoom: 8,
            continuousWorld: true  // Allow continuous world view
        }).setView([20, 0], 2);

        L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        const cableLayer = L.layerGroup().addTo(map);
        const landingPointLayer = L.layerGroup().addTo(map);

        // 🎯 LAYER ORDERING: Ensure landing points always appear on top of cables
        landingPointLayer.setZIndex(1000);

        // Create info control for hover information
        const info = L.control();
        info.onAdd = function (map) {
            this._div = L.DomUtil.create('div', 'info');
            this.update();
            return this._div;
        };

        // 🎯 CREATE MAP LEGEND: Landing Point Color Coding
        const landingPointLegend = L.control({ position: 'bottomright' });
        landingPointLegend.onAdd = function (map) {
            const div = L.DomUtil.create('div', 'landing-point-legend');
            div.innerHTML = `
                <div class="legend-title">Landing Points</div>
                <div class="legend-item">
                    <span class="legend-color red-point"></span>
                    <span class="legend-text">Default & Cable Selection</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color green-point"></span>
                    <span class="legend-text">Search Results</span>
                </div>
            `;
            return div;
        };
        landingPointLegend.addTo(map);
        info.update = function (props) {
            this._div.innerHTML = '<h4>Submarine Cable Info</h4>' +  (props ?
                '<b>' + props.name + '</b><br />' +
                (props.rfs ? 'RFS: ' + props.rfs + '<br />' : '') +
                (props.length ? 'Length: ' + props.length + ' km<br />' : '') +
                (props.owners ? 'Owners: ' + props.owners : '')
                : 'Hover over a cable');
        };
        info.addTo(map);

        const professionalColorPalette = [
            '#2E86AB','#A23B72', '#F18F01',
            '#C73E1D','#6C757D', '#495057',
            '#4A90A4', '#8E44AD', '#D35400',
            '#27AE60', '#2C3E50', '#8B4513',
            '#556B2F', '#4682B4', '#CD853F',
            '#708090', '#2F4F4F', '#800080',
            '#B22222', '#228B22', '#4169E1',
            '#DC143C', '#FF8C00', '#9932CC',
            '#8FBC8F', '#483D8B', '#2E8B57',
            '#B8860B', '#A0522D', '#1E90FF',
            '#32CD32', '#FF6347','#4B0082',
            '#DAA520', '#008B8B', '#9400D3',
            '#FF4500', '#2E8B57', '#8B008B',
            '#556B2F'
        ];

        // Helper: deep-clone a feature and shift all longitudes by delta (±360)
        function shiftFeatureLongitudes(feature, delta) {
            const clone = JSON.parse(JSON.stringify(feature));
            function shiftCoords(coords) {
                if (!coords) return coords;
                if (Array.isArray(coords[0]) || Array.isArray(coords[1])) {
                    return coords.map(shiftCoords);
                }
                if (typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    // [lng, lat]
                    return [coords[0] + delta, coords[1]];
                }
                return coords;
            }
            if (clone.geometry && clone.geometry.coordinates) {
                clone.geometry.coordinates = shiftCoords(clone.geometry.coordinates);
            }
            return clone;
        }

        // Helper: create a wrapped GeoJSON with copies shifted by ±360°
        function createWrappedGeoJSON(geojson) {
            if (!geojson || !geojson.features) return geojson;
            const wrapped = {
                ...geojson,
                features: []
            };
            for (const f of geojson.features) {
                wrapped.features.push(f);
                wrapped.features.push(shiftFeatureLongitudes(f, 360));
                wrapped.features.push(shiftFeatureLongitudes(f, -360));
            }
            return wrapped;
        }

        const specialCableColors = {
            'atlantic-crossing-1-ac-1': '#FF8C00',
            '2africa': '#000000',
            'africa-coast-to-europe-ace': '#DC143C',
            'west-africa-cable-system-wacs': '#4169E1',
            'maroc-telecom-west-africa': '#9932CC',
            'sat-3wasc': '#FF6347',
            // SEA-ME-WE cables - distinct professional colors
            'seamewe-3': '#1B4332',           // Dark forest green
            'seamewe-4': '#2D1B69',           // Dark purple
            'seamewe-5': '#8B0000',           // Dark red
            'seamewe-6': '#191970',           // Midnight blue
            // AAE and major Asian cables
            'asia-africa-europe-1-aae-1': '#4A4A4A',  // Dark gray
            'flag-europe-asia-fea': '#800000',         // Maroon
            // SEACOM and TATA cables
            'seacomtata-tgn-eurasia': '#2F4F4F',      // Dark slate gray
            'te-northtgn-eurasiaseacomalexandrosmedex': '#483D8B', // Dark slate blue
            // TATA TGN cables - different shades of professional colors
            'tata-tgn-atlantic': '#556B2F',           // Dark olive green
            'tata-tgn-western-europe': '#8B4513',     // Saddle brown
            'tata-tgn-pacific': '#2E4B4B',            // Dark teal
            'tata-tgn-intra-asia-tgn-ia': '#4B0082',  // Indigo
            'tata-tgn-tata-indicom': '#654321',       // Dark brown
            'tata-tgn-gulf': '#36454F'                // Charcoal
        };

        function getProfessionalCableColor(cableId) {
            if (specialCableColors.hasOwnProperty(cableId)) {
                return specialCableColors[cableId];
            }

            // Create a consistent hash based only on cable ID
            let hash = 0;
            for (let i = 0; i < cableId.length; i++) {
                const char = cableId.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }

            // Use only the hash for color determination to ensure consistency
            const colorIndex = Math.abs(hash) % professionalColorPalette.length;
            return professionalColorPalette[colorIndex];
        }


        // Americas countries (North, Central, and South America)
        const americasCountries = new Set([
            // North America
            'United States', 'Canada', 'Mexico', 'Greenland',
            'Guatemala', 'Belize', 'El Salvador', 'Honduras', 'Nicaragua', 'Costa Rica', 'Panama',
            'Cuba', 'Jamaica', 'Haiti', 'Dominican Republic', 'Puerto Rico', 'Bahamas', 'Barbados',
            'Trinidad and Tobago', 'Grenada', 'Saint Vincent and the Grenadines', 'Saint Lucia',
            'Dominica', 'Antigua and Barbuda', 'Saint Kitts and Nevis', 'Martinique', 'Guadeloupe',
            'Saint Barthélemy', 'Saint Martin', 'Sint Maarten', 'Anguilla', 'British Virgin Islands',
            'Virgin Islands (U.S.)', 'Virgin Islands (U.K.)', 'Cayman Islands', 'Turks and Caicos Islands',
            'Aruba', 'Curaçao', 'Bonaire, Sint Eustatius and Saba', 'Netherlands', 'French Guiana',
            'Brazil', 'Argentina', 'Chile', 'Peru', 'Colombia', 'Venezuela', 'Ecuador', 'Bolivia',
            'Paraguay', 'Uruguay', 'Guyana', 'Suriname', 'French Guiana'
        ]);
        // Define Asia-Pacific countries and territories
        const asiaPacificCountries = new Set([
            'China', 'Japan', 'South Korea', 'North Korea', 'Taiwan', 'Hong Kong', 'Macau', 'Mongolia',
            'Singapore', 'Indonesia', 'Philippines', 'Malaysia', 'Vietnam', 'Thailand', 'Myanmar',
            'Cambodia', 'Laos', 'Brunei', 'Timor-Leste',
            'India', 'Pakistan', 'Bangladesh', 'Sri Lanka', 'Nepal', 'Bhutan', 'Maldives', 'Afghanistan',
            'Australia', 'New Zealand', 'Papua New Guinea', 'Fiji', 'Solomon Islands', 'Vanuatu',
            'New Caledonia', 'Samoa', 'Tonga', 'Kiribati', 'Tuvalu', 'Nauru', 'Palau', 'Marshall Islands',
            'Micronesia', 'Cook Islands', 'French Polynesia', 'Wallis and Futuna', 'American Samoa',
            'Guam', 'Northern Mariana Islands', 'Cocos (Keeling) Islands', 'Christmas Island'
        ]);

        function isAmericasCable(cableId) {
            return false;
        }
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;
                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    return lng >= -180 && lng <= -25;
                }
                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }
        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

                    return inMainAsiaPacific || inPacificExtension;
                }
                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }

        // Define African countries
        const africaCountries = new Set([
            'Algeria', 'Angola', 'Benin', 'Botswana', 'Burkina Faso', 'Burundi', 'Cameroon',
            'Cape Verde', 'Central African Republic', 'Chad', 'Comoros', 'Congo', 'Congo, Dem. Rep.',
            'Congo, Rep.', 'Côte d\'Ivoire', 'Djibouti', 'Egypt', 'Equatorial Guinea', 'Eritrea',
            'Ethiopia', 'Gabon', 'Gambia', 'Ghana', 'Guinea', 'Guinea-Bissau', 'Kenya', 'Lesotho',
            'Liberia', 'Libya', 'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco',
            'Mozambique', 'Namibia', 'Niger', 'Nigeria', 'Rwanda', 'São Tomé and Príncipe', 'Senegal',
            'Seychelles', 'Sierra Leone', 'Somalia', 'South Africa', 'South Sudan', 'Sudan', 'Swaziland',
            'Tanzania', 'Togo', 'Tunisia', 'Uganda', 'Zambia', 'Zimbabwe'
        ]);
        // Define European countries
        const europeCountries = new Set([
            'Albania', 'Andorra', 'Austria', 'Belarus', 'Belgium', 'Bosnia and Herzegovina', 'Bulgaria',
            'Croatia', 'Cyprus', 'Czech Republic', 'Denmark', 'Estonia', 'Finland', 'France', 'Germany',
            'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy', 'Latvia', 'Liechtenstein', 'Lithuania',
            'Luxembourg', 'Malta', 'Moldova', 'Monaco', 'Montenegro', 'Netherlands', 'North Macedonia',
            'Norway', 'Poland', 'Portugal', 'Romania', 'Russia', 'San Marino', 'Serbia', 'Slovakia',
            'Slovenia', 'Spain', 'Sweden', 'Switzerland', 'Ukraine', 'United Kingdom', 'Vatican City'
        ]);
        function isAfricanCountry(country) {
            return africaCountries.has(country);
        }

        function isEuropeanCountry(country) {
            return europeCountries.has(country);
        }

        function isAfricaEuropeCountry(country) {
            return isAfricanCountry(country) || isEuropeanCountry(country);
        }

        function isAfricaEuropeConnection(country1, country2) {
            const isCountry1AfricaEurope = isAfricaEuropeCountry(country1);
            const isCountry2AfricaEurope = isAfricaEuropeCountry(country2);
            return isCountry1AfricaEurope && isCountry2AfricaEurope;
        }
        // Verify that a cable is visible on the filtered map
        function isCableVisibleOnMap(cableId) {
            return filteredCables.some(cable => cable.properties.id === cableId);
        }
        // Filter search results to only include cables visible on the map
        function filterSearchResultsToVisibleCables(searchResults) {
            if (Array.isArray(searchResults)) {
                // Direct array of cables
                const filtered = searchResults.filter(cable => {
                    const isVisible = isCableVisibleOnMap(cable.properties.id);

                    return isVisible;
                });
                return filtered;
            } else if (searchResults && typeof searchResults === 'object') {
                // Object with direct and multiHop arrays
                const filtered = {
                    direct: searchResults.direct ? filterSearchResultsToVisibleCables(searchResults.direct) : [],
                    multiHop: searchResults.multiHop ? filterSearchResultsToVisibleCables(searchResults.multiHop) : []
                };
                return filtered;
            }
            return searchResults;
        }

        // Fetch and display cable routes
        fetch('../cable/cable-geo.json')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {

                const criticalAfricanCables = new Set([
                    '2africa','west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'asia-africa-europe-1-aae-1',
                    'safe','sat-3wasc',
                    'equiano','africa-1',
                    'seychelles-to-east-africa-system-seas',
                    'the-east-african-marine-system-teams',
                    'seacomtata-tgn-eurasia',
                    'atlantic-crossing-1-ac-1',  // Important Europe-Americas cable (NL-UK-DE-US)
                    // Cyprus cables - critical for Mediterranean routing
                    'turcyos-1', 'turcyos-2', 'cadmos', 'tamares-north', 'poseidon',
                    // European interconnection cables
                    'c-lion1', 'finland-estonia-connection-fec', 'finland-estonia-2-eesf-2', 'finland-estonia-3-eesf-3',
                    'sweden-finland-4-sfs-4', 'sweden-finland-link-sfl', 'sweden-estonia-ee-s-1', 'sweden-latvia',
                    'latvia-sweden-1-lv-se-1', 'denmark-sweden-15', 'denmark-sweden-16', 'denmark-sweden-17', 'denmark-sweden-18',
                    'denmark-poland-2', 'germany-denmark-3', 'nordbalt', 'baltic-sea-submarine-cable', 'baltica',
                    // Mediterranean and European cables
                    'italy-greece-1', 'italy-croatia', 'italy-albania', 'italy-malta', 'italy-monaco', 'italy-libya',
                    'adria-1', 'trans-adriatic-express', 'ionian', 'minoas-east-and-west', 'apollo-east-and-west',
                    'thetis', 'blue', 'medusa-submarine-cable-system', 'go-1-mediterranean-cable-system',
                    // Turkey and Eastern Mediterranean
                    'caucasus-cable-system', 'kafos', 'berytar', 'aletar', 'ugarit',
                    // UK and Ireland connections including Amitie
                    'celtic-norse', 'celtixconnect-1-cc-1', 'havhingstenceltixconnect-2-cc-2', 'crosschannel-fibre',
                    'pan-european-crossing-uk-ireland', 'pan-european-crossing-uk-belgium', 'rockabill', 'amitie',
                    'scotland-northern-ireland-1', 'scotland-northern-ireland-2', 'scotland-northern-ireland-3', 'scotland-northern-ireland-4',
                    // France connections
                    'groix-4', 'penbal-4', 'penbal-5', 'pencan-8', 'pencan-9', 'corse-continent-4-cc4', 'corse-continent-5-cc5',
                    // Iberian connections
                    'romulo', 'almera-melilla-alme', 'roquetas-melilla-cam', 'estepona-tetouan', 'trapani-kelibia',
                    // Other European critical cables
                    'europe-india-gateway-eig', 'seamewe-3', 'seamewe-4', 'seamewe-5', 'seamewe-6', 'imewe',
                    'flag-europe-asia-fea', 'te-northtgn-eurasiaseacomalexandrosmedex'
                ]);

                // Show ALL cables worldwide (no regional filtering)
                const filteredFeatures = data.features;
                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };
                // Add wrapped copies to show cables across the 180° meridian
                const wrappedCables = createWrappedGeoJSON(filteredData);
                // Clear layer then add wrapped set
                cableLayer.clearLayers();
                L.geoJSON(wrappedCables, {
                    style: function(feature) { return getCableStyle(feature); },
                    onEachFeature: function(feature, layer) {
                        layer.on({
                            mouseover: function(e) {
                                if (!isIndividualCableSelected) {
                                    const l = e.target;
                                    l.setStyle({ weight: 4, opacity: 1 });
                                    info.update(feature.properties);
                                    if (!isLandingPointFilterActive) {
                                        previewLandingPointsForCable(feature.properties.id);
                                    }
                                }
                            },
                            mouseout: function(e) {
                                if (!isIndividualCableSelected) {
                                    const l = e.target;
                                    l.setStyle({ weight: 2.5, opacity: 0.85 });
                                    info.update();
                                    clearLandingPointPreview();
                                }
                            },
                            click: function(e) {
                                selectIndividualCable(feature.properties.id, feature);
                                L.DomEvent.stopPropagation(e);
                            }
                        });
                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                        if (feature.properties.rfs) popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                        if (feature.properties.length) popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                        if (feature.properties.owners) popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                        popupContent += '</div>';
                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);


                setTimeout(() => {
                    validateCableIdentification();
                }, 1000);
            })
            .catch(error => {
                console.error('❌ CRITICAL ERROR: Failed to load cable data:', error);
                console.error('📁 Missing file: cable-geo.json');
                console.error('🔧 This file contains the cable route geometries');
                console.error('💡 Without this file, the map cannot display cables');

                // Show user-friendly error message
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 20px;
                    background: #ff4444;
                    color: white;
                    padding: 15px;
                    border-radius: 5px;
                    z-index: 10000;
                    max-width: 400px;
                    font-family: Arial, sans-serif;
                    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                `;
                errorDiv.innerHTML = `
                    <strong>⚠️ Cable Data Missing</strong><br>
                    <small>The cable routes file is missing.<br>
                    Check the console for technical details.</small>
                `;
                document.body.appendChild(errorDiv);

                // Auto-hide after 10 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 10000);
            });

        function isLandingPointInAmericas(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            return lng >= -180 && lng <= -25;
        }

        // Function to check if landing point coordinates are in Asia-Pacific region
        function isLandingPointInAsiaPacific(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            const lat = coordinates[1];

            const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
            const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

            return inMainAsiaPacific || inPacificExtension;
        }

        // Fetch and display landing points
        fetch('../landing-point/landing-point-geo.json')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {

                // Critical African landing points that should always be preserved
                const criticalAfricanLandingPoints = new Set([
                    'cape-town-south-africa',
                    'mtunzini-south-africa',
                    'port-elizabeth-south-africa',
                    'gqeberha-south-africa',
                    'mombasa-kenya',
                    'dar-es-salaam-tanzania',
                    'djibouti-city-djibouti','lagos-nigeria',
                    'accra-ghana','dakar-senegal',
                    'casablanca-morocco',
                    'alexandria-egypt',
                    'port-said-egypt',
                    'zafarana-egypt',
                    'mumbai-india',
                    'maputo-mozambique',
                    'jeddah-saudi-arabia'
                ]);

                // Show ALL landing points worldwide (no regional filtering)
                const filteredFeatures = data.features;

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                // Add wrapped landing points too so they repeat when panning
                const wrappedLandingPoints = createWrappedGeoJSON(filteredData);
                L.geoJSON(wrappedLandingPoints, {
                    pointToLayer: (feature, latlng) => {
                        return L.circleMarker(latlng, {
                            radius: 3,
                            fillColor: '#FF0000',
                            color: '#FFFFFF',
                            weight: 1,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: (feature, layer) => {
                        if (feature.properties && feature.properties.id) {
                            // Only track the original (unshifted) landing point ID once
                            if (!allLandingPointLayers.has(feature.properties.id)) {
                                allLandingPointLayers.set(feature.properties.id, layer);
                            }
                        }
                        if (feature.properties && feature.properties.name) {
                            layer.bindPopup(`<b>${feature.properties.name}</b><br>${feature.properties.country || ''}`);
                        }
                        layer.bringToFront();
                    }
                }).addTo(landingPointLayer);

                // 🎯 FINAL LAYER ORDERING: Ensure all landing points are on top after initial load
                setTimeout(() => {
                    allLandingPointLayers.forEach((layer) => {
                        layer.bringToFront();
                    });
                    console.log('🎯 Landing points brought to front after initial load');
                }, 100);
            })
            .catch(error => {
                console.error('❌ CRITICAL ERROR: Failed to load landing points:', error);
                console.error('📁 Missing file: landing-point-geo.json');
                console.error('🔧 This file contains the geographic coordinates for all landing points');
                console.error('💡 Without this file, the map cannot display landing points');

                // Show user-friendly error message
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #ff4444;
                    color: white;
                    padding: 15px;
                    border-radius: 5px;
                    z-index: 10000;
                    max-width: 400px;
                    font-family: Arial, sans-serif;
                    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                `;
                errorDiv.innerHTML = `
                    <strong>⚠️ Map Data Missing</strong><br>
                    <small>The landing point coordinates file is missing.<br>
                    Check the console for technical details.</small>
                `;
                document.body.appendChild(errorDiv);

                // Auto-hide after 10 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 10000);
            });

        // Add layer control with improved tile options
        const baseMaps = {
            "CartoDB Positron": L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "Esri World Street": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, DeLorme, NAVTEQ, USGS, Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012',
                maxZoom: 19
            }),
            "CartoDB Voyager": L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            }),
            "Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 19
            })
        };

        const overlayMaps = {
            "Submarine Cables": cableLayer,
            "Landing Points": landingPointLayer
        };

        L.control.layers(baseMaps, overlayMaps).addTo(map);

        L.control.scale().addTo(map);

        let allCables = [];
        let filteredCables = []; // Cables visible on map after geographic filtering (synchronized with map display)
        let allLandingPoints = [];
        let availableCountries = new Set();
        let availableLandingPoints = new Map(); // Map: landing point name -> {country, fullName}
        let originalCableStyles = new Map();
        let isSearchActive = false;
        let currentCableIndex = 0;
        let cableCarouselActive = false;
        let currentCableNameLabel = null; // Store current cable name label
        let selectedCableId = null; // Track currently selected cable for individual highlighting
        let isIndividualCableSelected = false; // Track if individual cable selection is active
        let allLandingPointLayers = new Map(); // Store all landing point layers for filtering
        let isLandingPointFilterActive = false; // Track if landing point filtering is active

        // Multi-hop routing variables
        let countryGraph = new Map(); // Adjacency list for country connections
        let cableConnections = new Map(); // Maps country pairs to cables that connect them

        // 🎯 SEARCH CONTEXT: Store current search context for path-specific filtering
        let currentSearchContext = null;
        let searchResultCables = new Map(); // Maps cable ID to cable data with path info

        function selectIndividualCable(cableId, cableFeature) {
            console.log(`🎯 CABLE SELECTION STARTED: ${cableId}`);

            selectedCableId = cableId;
            isIndividualCableSelected = true;

            // 🎯 FORCE CONSISTENT FILTERING: Always clear and apply filtering immediately
            console.log(`🎯 Step 1: Force clearing all landing point filters`);

            // Immediately clear any existing filter state
            isLandingPointFilterActive = false;

            // Show all landing points first to reset state
            allLandingPointLayers.forEach((layer, landingPointId) => {
                layer.setStyle({
                    radius: 3,              // 🎯 CONSISTENT SIZE: Same as initial load (3px)
                    fillColor: '#FF0000',   // Red fill
                    color: '#FFFFFF',       // 🎯 WHITE BORDER: Match initial load
                    weight: 1,              // 🎯 CONSISTENT BORDER: Match initial load weight
                    opacity: 1,             // 🎯 FULL OPACITY: Match initial load for consistency
                    fillOpacity: 0.8        // 🎯 CONSISTENT TRANSPARENCY: Match initial load
                });
                if (!landingPointLayer.hasLayer(layer)) {
                    landingPointLayer.addLayer(layer);
                }
                // 🎯 LAYER ORDERING: Ensure landing points stay on top
                layer.bringToFront();
            });

            console.log(`🎯 Step 2: Applying cable-specific filtering for: ${cableId}`);

            // Apply filtering with a small delay to ensure state is reset
            setTimeout(() => {
                smartFilterLandingPointsForCable(cableId);
            }, 10);
            // Store original styles for all cables (always refresh to handle dynamic loading)
            cableLayer.eachLayer(layer => {
                if (layer.feature && !originalCableStyles.has(layer.feature.properties.id)) {
                    const cableIndex = Array.from(cableLayer.getLayers()).indexOf(layer);
                    const originalColor = getProfessionalCableColor(layer.feature.properties.id);
                    originalCableStyles.set(layer.feature.properties.id, {
                        color: originalColor,
                        opacity: 0.85,
                        weight: 2.5
                    });
                }
            });

            let selectedLayer = null;

            let layersProcessed = 0;
            let selectedLayerFound = false;

            // Function to process individual feature layers (h
            function processLayer(layer, depth = 0) {
                layersProcessed++;
                const indent = '  '.repeat(depth);

                if (layer.feature && layer.feature.properties) {
                    // This is an individual feature layer
                    const layerCableId = layer.feature.properties.id;
                    const layerCableName = layer.feature.properties.name;

                    if (layerCableId === cableId) {
                        selectedLayer = layer;
                        selectedLayerFound = true;

                        // Get or calculate original style
                        let originalStyle = originalCableStyles.get(layerCableId);
                        if (!originalStyle) {
                            const originalColor = getProfessionalCableColor(layerCableId);
                            originalStyle = {
                                color: originalColor,
                                opacity: 0.85,
                                weight: 2.5
                            };
                            originalCableStyles.set(layerCableId, originalStyle);
                        }

                        layer.setStyle({
                            color: originalStyle.color,
                            opacity: 1.0,
                            weight: 4.5,
                            dashArray: null
                        });
                        // Bring to front for better visibility
                        layer.bringToFront();

                    } else {
                        // Fade all other cables - reduce opacity and lighten color
                        layer.setStyle({
                            color: '#bbb',
                            opacity: 0.25,
                            weight: 1.5,
                            dashArray: null
                        });
                    }
                } else if (layer.eachLayer) {
                    // This is a layer group, process its children
                    layer.eachLayer(childLayer => processLayer(childLayer, depth + 1));
                }
            }
            // Process all layers (including nested ones)
            cableLayer.eachLayer(layer => processLayer(layer));

            if (selectedLayer) {
                try {
                    const bounds = selectedLayer.getBounds();
                    if (bounds && bounds.isValid()) {
                        map.fitBounds(bounds, {
                            padding: [60, 60],
                            maxZoom: 6,
                            animate: true,
                            duration: 1.2
                        });
                        // Show popup after zoom completes
                        setTimeout(() => {
                            if (selectedLayer.getPopup()) {
                                selectedLayer.openPopup();
                            }
                        }, 1300);
                    }
                } catch (error) {
                    if (cableFeature && cableFeature.geometry) {
                        zoomToCableGeometry(cableFeature);
                    }
                }
            }
        }

        // 🎯 SMART FILTERING: Context-aware landing point filtering
        async function smartFilterLandingPointsForCable(cableId) {
            console.log(`🎯 SMART FILTERING CALLED for cable: ${cableId}`);

            if (!cableId) {
                console.log(`🎯 No cable ID provided - clearing filter`);
                clearLandingPointFilter();
                return;
            }

            // Debug current state
            console.log(`🎯 Current state:`, {
                hasSearchContext: !!currentSearchContext,
                searchResultCablesSize: searchResultCables.size,
                cableInSearchResults: searchResultCables.has(cableId),
                searchContextData: currentSearchContext
            });

            // Check if we're in search context and this cable is from search results
            if (currentSearchContext && searchResultCables.has(cableId)) {
                console.log(`🔍 ✅ SEARCH CONTEXT DETECTED - applying path-specific filtering for cable: ${cableId}`);

                const cableData = searchResultCables.get(cableId);
                console.log(`🔍 Cable data:`, {
                    pathType: cableData.pathType,
                    pathInfo: cableData.pathInfo,
                    searchContext: currentSearchContext
                });

                // Apply path-specific filtering based on cable type
                await filterLandingPointsWithSearchContext(cableId, cableData, currentSearchContext);
            } else {
                console.log(`🔍 ❌ NO SEARCH CONTEXT - applying standard filtering for cable: ${cableId}`);
                console.log(`🔍 Reasons:`, {
                    noSearchContext: !currentSearchContext,
                    cableNotInResults: !searchResultCables.has(cableId),
                    availableCables: Array.from(searchResultCables.keys())
                });
                // Use standard filtering for manual cable selection
                await filterLandingPointsForCable(cableId);
            }
        }

        // 🎯 PATH-SPECIFIC FILTERING: Filter landing points based on search context
        async function filterLandingPointsWithSearchContext(cableId, cableData, searchContext) {
            // Wait for landing points to load if not ready
            if (allLandingPointLayers.size === 0) {
                console.log(`⏳ Landing points not loaded yet, waiting for ${cableId}...`);
                setTimeout(() => filterLandingPointsWithSearchContext(cableId, cableData, searchContext), 500);
                return;
            }

            try {
                // Load cable data to get its landing points
                const actualCableData = cableData.cableData || await loadCableData(cableId);
                if (!actualCableData || !actualCableData.landing_points) {
                    console.warn(`No landing points found for cable: ${cableId}`);
                    return;
                }

                // Ensure landing point layer is on the map
                if (!map.hasLayer(landingPointLayer)) {
                    console.log(`🔧 Adding landing point layer to map`);
                    map.addLayer(landingPointLayer);
                }

                let relevantLandingPointIds = new Set();

                // 🎯 DETERMINE RELEVANT LANDING POINTS BASED ON PATH TYPE
                if (cableData.pathType === 'multi-hop' && cableData.pathInfo) {
                    // Multi-hop: show only landing points in segment countries
                    const segmentFrom = cableData.pathInfo.segmentFrom;
                    const segmentTo = cableData.pathInfo.segmentTo;

                    console.log(`🔗 Multi-hop segment: ${segmentFrom} ➜ ${segmentTo}`);

                    actualCableData.landing_points.forEach(lp => {
                        if (lp.id && lp.country && (lp.country === segmentFrom || lp.country === segmentTo)) {
                            relevantLandingPointIds.add(lp.id);
                            console.log(`  ✅ Segment landing point: ${lp.name} (${lp.country})`);
                        }
                    });
                } else if (cableData.pathType === 'direct') {
                    // Direct: show only landing points in origin/destination countries
                    console.log(`🔗 Direct connection: ${searchContext.fromCountry} ➜ ${searchContext.toCountry}`);

                    actualCableData.landing_points.forEach(lp => {
                        if (lp.id && lp.country &&
                            (lp.country === searchContext.fromCountry || lp.country === searchContext.toCountry)) {
                            relevantLandingPointIds.add(lp.id);
                            console.log(`  ✅ Direct landing point: ${lp.name} (${lp.country})`);
                        }
                    });
                } else {
                    // Fallback: show all landing points
                    console.log(`🔗 Fallback: showing all landing points for ${cableId}`);
                    actualCableData.landing_points.forEach(lp => {
                        if (lp.id) relevantLandingPointIds.add(lp.id);
                    });
                }

                console.log(`🎯 Path-specific filtering: ${relevantLandingPointIds.size} relevant points for cable ${cableId}`);

                let showCount = 0;
                let hideCount = 0;

                // Apply filtering with GREEN styling (consistent with search results)
                allLandingPointLayers.forEach((layer, landingPointId) => {
                    if (relevantLandingPointIds.has(landingPointId)) {
                        // Show relevant landing points with GREEN styling
                        layer.setStyle({
                            radius: 3,              // 🎯 CONSISTENT SIZE: Same as default (3px)
                            fillColor: '#00FF00',   // GREEN - search result filtering
                            color: '#FFFFFF',       // White border for better contrast
                            weight: 1,              // Standard border weight
                            opacity: 1,             // Full opacity for filtered state
                            fillOpacity: 0.8        // Slightly transparent
                        });
                        if (!landingPointLayer.hasLayer(layer)) {
                            landingPointLayer.addLayer(layer);
                        }
                        layer.bringToFront();
                        showCount++;
                    } else {
                        // Hide irrelevant landing points
                        if (landingPointLayer.hasLayer(layer)) {
                            landingPointLayer.removeLayer(layer);
                        }
                        hideCount++;
                    }
                });

                isLandingPointFilterActive = true;
                console.log(`✅ Path-specific filter applied: ${showCount} GREEN points shown, ${hideCount} points hidden`);

            } catch (error) {
                console.error(`Error applying path-specific filtering for cable ${cableId}:`, error);
                // Fallback to standard filtering
                await filterLandingPointsForCable(cableId);
            }
        }

        // Function to filter landing points for a specific cable with consistency guarantee
        async function filterLandingPointsForCable(cableId) {
            console.log(`🎯 STANDARD FILTERING CALLED for cable: ${cableId}`);

            if (!cableId) {
                clearLandingPointFilter();
                return;
            }

            // 🎯 CONSISTENCY CHECK: Ensure landing points are loaded before filtering
            if (allLandingPointLayers.size === 0) {
                console.log(`⏳ Landing points not ready for standard filtering, waiting...`);
                setTimeout(() => filterLandingPointsForCable(cableId), 100);
                return;
            }

            try {
                // Load cable data to get its landing points
                const cableData = await loadCableData(cableId);
                if (!cableData || !cableData.landing_points) {
                    console.warn(`No landing points found for cable: ${cableId}`);
                    return;
                }

                // Get the landing point IDs for this cable
                const cableLandingPointIds = new Set(
                    cableData.landing_points.map(lp => lp.id).filter(Boolean)
                );

                console.log(`Filtering landing points for cable ${cableId}:`, cableLandingPointIds);

                console.log(`🎯 STANDARD FILTERING: Showing ${cableLandingPointIds.size} landing points for cable ${cableId}`);
                console.log(`🎯 Landing point IDs:`, Array.from(cableLandingPointIds));

                let showCount = 0;
                let hideCount = 0;

                // Filter landing points to show only relevant ones
                allLandingPointLayers.forEach((layer, landingPointId) => {
                    if (cableLandingPointIds.has(landingPointId)) {
                        // Show relevant landing points with RED styling (standard cable selection)
                        layer.setStyle({
                            radius: 3,              // 🎯 CONSISTENT SIZE: Same as default (3px)
                            fillColor: '#FF0000',   // RED - direct cable selection filtering
                            color: '#FFFFFF',       // White border for better contrast
                            weight: 1,              // Normal border weight
                            opacity: 1,             // Full opacity for filtered state
                            fillOpacity: 0.8        // Slightly transparent
                        });
                        if (!landingPointLayer.hasLayer(layer)) {
                            landingPointLayer.addLayer(layer);
                        }
                        layer.bringToFront();
                        showCount++;
                    } else {
                        // Hide irrelevant landing points
                        if (landingPointLayer.hasLayer(layer)) {
                            landingPointLayer.removeLayer(layer);
                        }
                        hideCount++;
                    }
                });

                isLandingPointFilterActive = true;
                console.log(`✅ STANDARD FILTER APPLIED: ${showCount} RED points shown, ${hideCount} points hidden for cable: ${cableId}`);

            } catch (error) {
                console.error(`Error filtering landing points for cable ${cableId}:`, error);
            }
        }

        // Function to clear landing point filter and show all points
        function clearLandingPointFilter() {
            if (!isLandingPointFilterActive) return;

            // Restore all landing points with consistent styling
            allLandingPointLayers.forEach((layer, landingPointId) => {
                layer.setStyle({
                    radius: 3,              // 🎯 CONSISTENT SIZE: Same as initial load (3px)
                    fillColor: '#FF0000',   // Original red color
                    color: '#FFFFFF',       // 🎯 WHITE BORDER: Match initial load
                    weight: 1,              // 🎯 CONSISTENT BORDER: Match initial load weight
                    opacity: 1,             // 🎯 FULL OPACITY: Match initial load for consistency
                    fillOpacity: 0.8        // 🎯 CONSISTENT TRANSPARENCY: Match initial load
                });
                if (!landingPointLayer.hasLayer(layer)) {
                    landingPointLayer.addLayer(layer);
                }
                // 🎯 LAYER ORDERING: Ensure landing points stay on top
                layer.bringToFront();
            });

            isLandingPointFilterActive = false;
            console.log('Landing point filter cleared - showing all points');
        }

        // Function to preview landing points on hover (lighter effect)
        async function previewLandingPointsForCable(cableId) {
            if (!cableId || isLandingPointFilterActive) return;

            try {
                const cableData = await loadCableData(cableId);
                if (!cableData || !cableData.landing_points) return;

                const cableLandingPointIds = new Set(
                    cableData.landing_points.map(lp => lp.id).filter(Boolean)
                );

                // Apply subtle preview styling
                allLandingPointLayers.forEach((layer, landingPointId) => {
                    if (cableLandingPointIds.has(landingPointId)) {
                        // Highlight relevant landing points with subtle effect
                        layer.setStyle({
                            radius: 4,              // 🎯 SMALLER PREVIEW: Reduced from 6 to 4
                            fillColor: '#FFA500',   // Orange for preview
                            color: '#FFFFFF',       // 🎯 WHITE BORDER: Better contrast
                            weight: 1,              // 🎯 THINNER BORDER: Reduced from 1.5 to 1
                            opacity: 1,             // Full opacity for preview
                            fillOpacity: 0.7        // Moderate transparency for preview
                        });
                    } else {
                        // Dim other landing points slightly
                        layer.setStyle({
                            radius: 2,              // 🎯 VERY SMALL: Reduced from 4 to 2 for dimmed state
                            fillColor: '#FF0000',   // Red fill
                            color: '#FFFFFF',       // 🎯 WHITE BORDER: Match new default
                            weight: 0.5,            // 🎯 VERY THIN BORDER: Reduced from 1 to 0.5
                            opacity: 0.4,           // 🎯 MORE TRANSPARENT: Reduced from 0.6 to 0.4
                            fillOpacity: 0.3        // 🎯 VERY TRANSPARENT: Reduced from 0.5 to 0.3
                        });
                    }
                });
            } catch (error) {
                console.warn(`Error previewing landing points for cable ${cableId}:`, error);
            }
        }

        // Function to clear preview effects
        function clearLandingPointPreview() {
            if (isLandingPointFilterActive) return; // Don't clear if filter is active

            // Restore consistent styling
            allLandingPointLayers.forEach((layer, landingPointId) => {
                layer.setStyle({
                    radius: 3,              // 🎯 CONSISTENT SIZE: Same as initial load (3px)
                    fillColor: '#FF0000',   // Red fill
                    color: '#FFFFFF',       // 🎯 WHITE BORDER: Match initial load
                    weight: 1,              // 🎯 CONSISTENT BORDER: Match initial load weight
                    opacity: 1,             // 🎯 FULL OPACITY: Match initial load for consistency
                    fillOpacity: 0.8        // 🎯 CONSISTENT TRANSPARENCY: Match initial load
                });
                // 🎯 LAYER ORDERING: Ensure landing points stay on top
                layer.bringToFront();
            });
        }

        function clearIndividualCableSelection() {

            if (!isIndividualCableSelected) {
                return; // Exit if no individual cable is selected
            }

            selectedCableId = null;
            isIndividualCableSelected = false;

            // Clear landing point filter when clearing cable selection
            clearLandingPointFilter();

            // Function to restore styles for individual feature layers
            function restoreLayer(layer, layerIndex = 0) {
                if (layer.feature && layer.feature.properties) {
                    const cableId = layer.feature.properties.id;
                    const originalColor = getProfessionalCableColor(cableId);

                    // Restore to normal style
                    layer.setStyle({
                        color: originalColor,
                        opacity: 0.85,
                        weight: 2.5,
                        dashArray: layer.feature.properties.is_planned ? '8, 4' : null
                    });
                    if (layer.getPopup()) {
                        layer.closePopup();
                    }
                } else if (layer.eachLayer) {
                    // This is a layer group, process its children
                    layer.eachLayer(childLayer => restoreLayer(childLayer, layerIndex));
                }
            }
            // Restore all cables to their normal appearance (including nested ones)
            let layerIndex = 0;
            cableLayer.eachLayer(layer => {
                restoreLayer(layer, layerIndex);
                layerIndex++;
            });
        }

        function zoomToCableGeometry(cableFeature) {
            if (!cableFeature.geometry || !cableFeature.geometry.coordinates) {
                return;
            }
            const coords = cableFeature.geometry.coordinates;
            let bounds = L.latLngBounds();

            function addCoordinatesToBounds(coordinates) {
                if (Array.isArray(coordinates[0])) {
                    if (Array.isArray(coordinates[0][0])) {
                        // MultiLineString
                        coordinates.forEach(lineString => {
                            lineString.forEach(coord => {
                                bounds.extend([coord[1], coord[0]]);
                            });
                        });
                    } else {
                        // LineString
                        coordinates.forEach(coord => {
                            bounds.extend([coord[1], coord[0]]);
                        });
                    }
                }
            }
            addCoordinatesToBounds(coords);

            if (bounds.isValid()) {
                map.fitBounds(bounds, {
                    padding: [60, 60],
                    maxZoom: 6,
                    animate: true,
                    duration: 1.2
                });
            }
        }
        // Add map click handler to clear search if active and close persistent popups
        map.on('click', function(e) {
            if (isSearchActive) {
                clearSearch();
            } else if (isIndividualCableSelected) {
                // Clear individual cable selection and restore normal view
                clearIndividualCableSelection();
            } else {
                // Clear any persistent cable name labels when clicking on empty map area
                if (typeof clearCableNameLabel === 'function') {
                    clearCableNameLabel();
                }
            }
        });

        // Load cable and landing point data for sidebar
        Promise.all([
            fetch('../cable/cable-geo.json').then(response => response.json()),
            fetch('../landing-point/landing-point-geo.json').then(response => response.json())
        ]).then(async ([cableData, landingPointData]) => {
            allCables = cableData.features;
            allLandingPoints = landingPointData.features;

            // Initialize filteredCables if not already set by map loading
            if (filteredCables.length === 0) {
                filteredCables = allCables;
            }

            await extractAvailableLandingPoints();
            setupSearchFunctionality();

            // Initialize country graph for multi-hop routing
            buildCountryGraph().then(() => {

            }).catch(error => {
            });
        });

        async function extractAvailableLandingPoints() {
            const africaCountries = new Set([
                'Algeria', 'Angola', 'Benin', 'Botswana', 'Burkina Faso', 'Burundi', 'Cameroon',
                'Cape Verde', 'Central African Republic', 'Chad', 'Comoros', 'Congo', 'Congo, Dem. Rep.',
                'Congo, Rep.', 'Côte d\'Ivoire', 'Djibouti', 'Egypt', 'Equatorial Guinea', 'Eritrea',
                'Ethiopia', 'Gabon', 'Gambia', 'Ghana', 'Guinea', 'Guinea-Bissau', 'Kenya', 'Lesotho',
                'Liberia', 'Libya', 'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco',
                'Mozambique', 'Namibia', 'Niger', 'Nigeria', 'Rwanda', 'São Tomé and Príncipe', 'Senegal',
                'Seychelles', 'Sierra Leone', 'Somalia', 'South Africa', 'South Sudan', 'Sudan', 'Swaziland',
                'Tanzania', 'Togo', 'Tunisia', 'Uganda', 'Zambia', 'Zimbabwe'
            ]);

            const europeCountries = new Set([
                'Albania', 'Andorra', 'Austria', 'Belarus', 'Belgium', 'Bosnia and Herzegovina', 'Bulgaria',
                'Croatia', 'Cyprus', 'Czech Republic', 'Denmark', 'Estonia', 'Finland', 'France', 'Germany',
                'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy', 'Latvia', 'Liechtenstein', 'Lithuania',
                'Luxembourg', 'Malta', 'Moldova', 'Monaco', 'Montenegro', 'Netherlands', 'North Macedonia',
                'Norway', 'Poland', 'Portugal', 'Romania', 'Russia', 'San Marino', 'Serbia', 'Slovakia',
                'Slovenia', 'Spain', 'Sweden', 'Switzerland', 'Ukraine', 'United Kingdom', 'Vatican City',
                'Jersey', 'Guernsey', 'Isle of Man'
            ]);

            // Clear previous data
            availableCountries.clear();
            availableLandingPoints.clear();

            // Extract landing points from actual cables that are visible on the map
            const cableLandingPoints = new Set();

            for (const cable of filteredCables) {
                try {
                    const cableData = await loadCableData(cable.properties.id);
                    if (cableData && cableData.landing_points) {
                        cableData.landing_points.forEach(landingPoint => {
                            if (landingPoint.name && landingPoint.country) {
                                const country = landingPoint.country.trim();
                                // Add country to available countries (global scope)
                                availableCountries.add(country);

                                // Add landing point to available landing points
                                const fullName = landingPoint.name;
                                availableLandingPoints.set(fullName, {
                                    country: country,
                                    fullName: fullName
                                });
                                cableLandingPoints.add(fullName);
                            }
                        });
                    }
                } catch (error) {
                    console.warn(`Failed to load cable data for ${cable.properties.id}:`, error);
                }
            }

            // Also extract from the landing points GeoJSON (as backup)
            allLandingPoints.forEach(point => {
                if (point.properties && point.properties.name) {
                    const fullName = point.properties.name;
                    const nameParts = fullName.split(',');
                    if (nameParts.length >= 2) {
                        const country = nameParts[nameParts.length - 1].trim();
                        // Add country to available countries (global scope)
                        availableCountries.add(country);

                        // Add landing point to available landing points (only if not already added from cables)
                        if (!availableLandingPoints.has(fullName)) {
                            availableLandingPoints.set(fullName, {
                                country: country,
                                fullName: fullName
                            });
                        }
                    }
                }
            });

            // Add some known countries that might be in the cable data but not easily extracted
            const knownAfricanEuropeanCountries = [
                'South Africa', 'Egypt', 'Morocco', 'Nigeria', 'Kenya', 'Ghana', 'Senegal', 'Tanzania',
                'Angola', 'Mozambique', 'Madagascar', 'Mauritius', 'Seychelles', 'Djibouti', 'Somalia',
                'United Kingdom', 'France', 'Spain', 'Italy', 'Germany', 'Netherlands', 'Portugal',
                'Greece', 'Norway', 'Denmark', 'Sweden', 'Finland', 'Ireland', 'Belgium', 'Malta',
                'Cyprus', 'Bulgaria', 'Romania', 'Croatia', 'Albania', 'Turkey', 'Russia',
                'Jersey', 'Guernsey', 'Isle of Man'
            ];

            knownAfricanEuropeanCountries.forEach(country => {
                if (africaCountries.has(country) || europeCountries.has(country)) {
                    availableCountries.add(country);
                }
            });

            console.log(`Extracted ${availableLandingPoints.size} landing points from ${cableLandingPoints.size} cable-connected points`);

            // Debug: Show landing points per country
            const countryLandingPoints = new Map();
            for (const [landingPoint, data] of availableLandingPoints) {
                const country = data.country;
                if (!countryLandingPoints.has(country)) {
                    countryLandingPoints.set(country, []);
                }
                countryLandingPoints.get(country).push(landingPoint);
            }

            // Log UK landing points specifically
            if (countryLandingPoints.has('United Kingdom')) {
                const ukPoints = countryLandingPoints.get('United Kingdom');
                console.log(`UK landing points (${ukPoints.length}):`, ukPoints.sort());
            }
        }

        function setupSearchFunctionality() {
            const fromCountryInput = document.getElementById('fromCountry');
            const toCountryInput = document.getElementById('toCountry');
            const fromDropdown = document.getElementById('fromCountryDropdown');
            const toDropdown = document.getElementById('toCountryDropdown');
            const searchBtn = document.getElementById('searchBtn');
            const clearBtn = document.getElementById('clearBtn');

            // Create combined search options: countries first, then landing points
            const searchOptions = [];

            // Add countries first (they will appear at the top of search results)
            const countriesArray = Array.from(availableCountries).sort();
            countriesArray.forEach(country => {
                searchOptions.push({
                    displayText: country,
                    searchValue: country,
                    type: 'country',
                    priority: 1 // Higher priority for countries
                });
            });

            // Add landing points after countries
            const landingPointsArray = Array.from(availableLandingPoints.keys()).sort();
            landingPointsArray.forEach(landingPoint => {
                searchOptions.push({
                    displayText: landingPoint,
                    searchValue: landingPoint,
                    type: 'landingPoint',
                    priority: 2 // Lower priority for landing points
                });
            });

            setupAutocomplete(fromCountryInput, fromDropdown, searchOptions);
            setupAutocomplete(toCountryInput, toDropdown, searchOptions);

            // Landing point filtering is now always active - no checkbox needed

            // Helper function to validate search input
            function isValidSearchInput(value) {
                // Check if it's a valid country
                if (availableCountries.has(value)) {
                    return true;
                }
                // Check if it's a valid landing point
                if (availableLandingPoints.has(value)) {
                    return true;
                }
                return false;
            }

            // Enable search button when both locations are selected
            function updateSearchButton() {
                const fromValid = isValidSearchInput(fromCountryInput.value);
                const toValid = isValidSearchInput(toCountryInput.value);
                searchBtn.disabled = !(fromValid && toValid && fromCountryInput.value !== toCountryInput.value);

                // Auto-clear search when both fields are empty
                if (fromCountryInput.value === '' && toCountryInput.value === '' && isSearchActive) {

                    // Brief visual feedback that search is being cleared
                    const resultsDiv = document.getElementById('cableResults');
                    if (resultsDiv) {
                        resultsDiv.innerHTML = '<div class="no-results">🔄 Restoring original map view...</div>';
                        setTimeout(() => {
                            clearSearchResults();
                        }, 300); // Small delay for visual feedback
                    } else {
                        clearSearchResults();
                    }
                }
            }

            fromCountryInput.addEventListener('input', updateSearchButton);
            toCountryInput.addEventListener('input', updateSearchButton);

            // Additional event listeners to catch all ways of clearing input
            fromCountryInput.addEventListener('keyup', updateSearchButton);
            toCountryInput.addEventListener('keyup', updateSearchButton);
            fromCountryInput.addEventListener('change', updateSearchButton);
            toCountryInput.addEventListener('change', updateSearchButton);

            // Search functionality
            searchBtn.addEventListener('click', performSearch);
            clearBtn.addEventListener('click', clearSearch);
        }

        // Helper function to convert search input to country name
        function getCountryFromSearchInput(searchInput) {
            // If it's already a country, return it
            if (availableCountries.has(searchInput)) {
                return searchInput;
            }
            // If it's a landing point, extract the country
            if (availableLandingPoints.has(searchInput)) {
                return availableLandingPoints.get(searchInput).country;
            }
            return null;
        }

        // Helper function to get all landing points for a country
        function getLandingPointsForCountry(country) {
            const landingPoints = [];
            for (const [landingPoint, data] of availableLandingPoints) {
                if (data.country === country) {
                    landingPoints.push(landingPoint);
                }
            }
            return landingPoints;
        }

        function setupAutocomplete(input, dropdown, searchOptions) {
            input.addEventListener('input', function() {
                const value = this.value.toLowerCase();
                dropdown.innerHTML = '';

                if (value.length === 0) {
                    dropdown.style.display = 'none';
                    return;
                }

                // Filter options based on search value
                let filtered = searchOptions.filter(option => {
                    const displayText = option.displayText.toLowerCase();
                    // Check if the search value matches the display text
                    if (displayText.includes(value)) {
                        return true;
                    }
                    // For landing points, also check if the search value matches the country
                    if (option.type === 'landingPoint') {
                        const landingPointData = availableLandingPoints.get(option.searchValue);
                        if (landingPointData && landingPointData.country.toLowerCase().includes(value)) {
                            return true;
                        }
                    }
                    return false;
                });

                // Sort filtered results: countries first, then landing points
                filtered.sort((a, b) => {
                    // First sort by priority (countries first)
                    if (a.priority !== b.priority) {
                        return a.priority - b.priority;
                    }
                    // Then sort alphabetically within the same priority
                    return a.displayText.localeCompare(b.displayText);
                });

                // Determine if we're searching for a specific country
                const isCountrySearch = Array.from(availableCountries).some(country =>
                    country.toLowerCase().includes(value)
                );

                // If searching for a country, show more results to include all landing points
                // Otherwise, limit to 15 results
                const resultLimit = isCountrySearch ? 50 : 15;
                filtered = filtered.slice(0, resultLimit);

                if (filtered.length > 0) {
                    filtered.forEach(option => {
                        const item = document.createElement('div');
                        item.className = 'dropdown-item';

                        // Style differently for countries vs landing points
                        if (option.type === 'country') {
                            item.innerHTML = `<strong>${option.displayText}</strong> <span style="color: #666; font-size: 0.9em;">(Country)</span>`;
                        } else {
                            const landingPointData = availableLandingPoints.get(option.searchValue);
                            item.innerHTML = `${option.displayText} <span style="color: #666; font-size: 0.9em;">(Landing Point)</span>`;
                        }

                        item.addEventListener('click', function() {
                            input.value = option.searchValue;
                            dropdown.style.display = 'none';
                            input.dispatchEvent(new Event('input'));
                        });
                        dropdown.appendChild(item);
                    });
                    dropdown.style.display = 'block';
                } else {
                    dropdown.style.display = 'none';
                }
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!input.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });
        }

        function getCableStyle(feature, cableIndex) {
            const cableColor = getProfessionalCableColor(feature.properties.id);
            const priority = feature.properties.priority || 'main';
            if (priority === 'main') {
                return {
                    color: cableColor,
                    weight: 3.5,
                    opacity: 0.85,
                    dashArray: null
                };
            } else {
                // Secondary/alternative: orange dashed
                return {
                    color: '#F18F01',
                    weight: 2.5,
                    opacity: 0.85,
                    dashArray: '6, 6'
                };
            }
        }

        let allCableGeoJSON = null;
        let currentCableGeoJSON = null;

        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                allCableGeoJSON = data;
            });

        // Helper to clear and add only matching cables
        function showOnlyCables(connectingCables) {
            cableLayer.clearLayers();
            if (!connectingCables || connectingCables.length === 0) return;
            // Build a GeoJSON with only the matching features
            const features = connectingCables.map(cable => cable);
            const filteredGeoJSON = {
                ...allCableGeoJSON,
                features: features.map(c => c.feature || c)
            };
            currentCableGeoJSON = filteredGeoJSON;
            L.geoJSON(filteredGeoJSON, {
                style: function(feature) {
                    const cableIndex = filteredGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    const cableIndex = filteredGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id);
                    layer.on({
                        mouseover: function(e) {
                            // Only apply hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1
                                });
                                info.update(feature.properties);
                            }
                        },
                        mouseout: function(e) {
                            // Only restore hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85
                                });
                                info.update();
                            }
                        },
                        click: function(e) {
                            selectIndividualCable(feature.properties.id, feature);
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }

        // Helper to restore all cables
        function restoreAllCables() {
            cableLayer.clearLayers();
            if (!allCableGeoJSON) return;
            L.geoJSON(allCableGeoJSON, {
                style: function(feature) {
                    const cableIndex = allCableGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    // (reuse your onEachFeature code from above)
                    const cableIndex = allCableGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id);
                    layer.on({
                        mouseover: function(e) {
                            // Only apply hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1
                                });
                                info.update(feature.properties);
                            }
                        },
                        mouseout: function(e) {
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85
                                });
                                info.update();
                            }
                        },
                        click: function(e) {
                            selectIndividualCable(feature.properties.id, feature);
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }
        function clearSearchResults() {

            // Hide dropdowns
            document.getElementById('fromCountryDropdown').style.display = 'none';
            document.getElementById('toCountryDropdown').style.display = 'none';

            // Hide results
            document.getElementById('resultsHeader').style.display = 'none';
            document.getElementById('cableResults').innerHTML = '';

            // Clear cable name label
            clearCableNameLabel();

            restoreAfricaEuropeCables();
            resetLandingPointHighlights();

            // 🎯 CLEAR SEARCH CONTEXT: Reset smart filtering context
            currentSearchContext = null;
            searchResultCables.clear();
            console.log('🔄 Search context cleared - cable clicks will now use standard filtering');

            isSearchActive = false;
        }

        function clearSearch() {
            document.getElementById('fromCountry').value = '';
            document.getElementById('toCountry').value = '';
            document.getElementById('searchBtn').disabled = true;

            clearSearchResults();
        }

        async function findConnectingCables(fromCountry, toCountry) {
            const connectingCables = [];

            document.getElementById('cableResults').innerHTML = '<div class="no-results">Searching cables...</div>';

            // We need to load individual cable files to get landing point information
            // Use filtered cables to match what's visible on the map
            const promises = filteredCables.map(async (cable) => {
                try {
                    const cableResponse = await fetch(`../cable/${cable.properties.id}.json`);
                    if (!cableResponse.ok) {
                        throw new Error(`HTTP ${cableResponse.status}`);
                    }
                    const cableData = await cableResponse.json();

                    if (cableData.landing_points && Array.isArray(cableData.landing_points)) {
                        const countries = cableData.landing_points.map(lp => lp.country).filter(Boolean);

                        if (countries.includes(fromCountry) && countries.includes(toCountry)) {
                            return {
                                ...cable,
                                cableData: cableData
                            };
                        }
                    }
                } catch (error) {
                    console.warn(`Could not load cable data for ${cable.properties.id}:`, error);
                }
                return null;
            });

            // Wait for all promises to resolve and filter out null results
            const results = await Promise.all(promises);
            const validResults = results.filter(result => result !== null);

            return validResults;
        }
        function updateMapVisualization(connectingCables) {

            // Store original styles if not already stored
            if (originalCableStyles.size === 0) {
                cableLayer.eachLayer(layer => {
                    if (layer.feature) {
                        originalCableStyles.set(layer.feature.properties.id, {
                            color: layer.options.color,
                            opacity: layer.options.opacity,
                            weight: layer.options.weight
                        });
                    }
                });
            }

            const connectingCableIds = new Set(connectingCables.map(cable => cable.properties.id));

            // Update all cable layers with extreme visibility contrast
            cableLayer.eachLayer(layer => {
                if (layer.feature) {
                    const cableId = layer.feature.properties.id;

                    if (connectingCableIds.has(cableId)) {
                        const originalStyle = originalCableStyles.get(cableId);
                        layer.setStyle({
                            color: originalStyle.color,
                            opacity: 1.0,
                            weight: 4,
                            dashArray: null
                        });

                        layer.bringToFront();

                    } else {
                        // BARELY VISIBLE: Make all other cables almost invisible
                        layer.setStyle({
                            color: '#cccccc',
                            opacity: 0.1,
                            weight: 1,
                            dashArray: null
                        });
                    }
                }
            });

            // Count total cables for verification
            let totalCables = 0;
            let highlightedCount = 0;
            let dimmedCount = 0;

            cableLayer.eachLayer(layer => {
                if (layer.feature) {
                    totalCables++;
                    const cableId = layer.feature.properties.id;
                    if (connectingCableIds.has(cableId)) {
                        highlightedCount++;
                    } else {
                        dimmedCount++;
                    }
                }
            });
        }
        function resetMapVisualization() {

            cableLayer.eachLayer(layer => {
                if (layer.feature && originalCableStyles.has(layer.feature.properties.id)) {
                    const originalStyle = originalCableStyles.get(layer.feature.properties.id);

                    // Apply original style smoothly
                    layer.setStyle({
                        color: originalStyle.color,
                        opacity: originalStyle.opacity,
                        weight: originalStyle.weight,
                        dashArray: layer.feature.properties.is_planned ? '8, 4' : null
                    });
                    if (layer.getPopup()) {
                        layer.closePopup();
                    }
                }
            });
        }

        // Helper function to format landing points for display
        function formatLandingPoints(landingPoints) {
            if (!landingPoints || !Array.isArray(landingPoints) || landingPoints.length === 0) {
                return '<div class="cable-landing-points"><div class="landing-points-label">Landing Points:</div><div style="font-style: italic; color: #95a5a6;">Not available</div></div>';
            }

            // Show detailed landing points with city and country information
            const landingPointsHtml = landingPoints.map((lp, index) => {
                const locationName = lp.name || `${lp.country || 'Unknown'}`;
                const arrow = index < landingPoints.length - 1 ? '<span class="route-arrow">➜</span>' : '';
                return `<span class="landing-point" title="${locationName}">${locationName}</span>${arrow}`;
            }).join('');

            // If there are too many landing points, show a summary
            if (landingPoints.length > 8) {
                const countries = [...new Set(landingPoints.map(lp => lp.country).filter(Boolean))];
                const routeHtml = countries.map((country, index) => {
                    const arrow = index < countries.length - 1 ? '<span class="route-arrow">➜</span>' : '';
                    return `<span class="landing-point">${country}</span>${arrow}`;
                }).join('');

                return `
                    <div class="cable-landing-points">
                        <div class="landing-points-label">Landing Points (${landingPoints.length} locations):</div>
                        <div class="landing-points-route">${routeHtml}</div>
                    </div>
                `;
            }

            return `
                <div class="cable-landing-points">
                    <div class="landing-points-label">Landing Points:</div>
                    <div class="landing-points-route">${landingPointsHtml}</div>
                </div>
            `;
        }

        function displaySearchResults(connectingCables, fromCountry, toCountry) {
            const resultsHeader = document.getElementById('resultsHeader');
            const cableResults = document.getElementById('cableResults');

            if (connectingCables.length === 0) {
                resultsHeader.style.display = 'block';
                resultsHeader.textContent = 'No cables found';
                cableResults.innerHTML = '<div class="no-results">No submarine cables found connecting these countries.</div>';
                return;
            }

            resultsHeader.style.display = 'block';
            resultsHeader.textContent = `Found ${connectingCables.length} cable${connectingCables.length > 1 ? 's' : ''}:`;

            cableResults.innerHTML = '';

            connectingCables.forEach(cable => {
                const cableDiv = document.createElement('div');
                cableDiv.className = 'cable-result';

                const actualCableColor = getActualCableColor(cable.properties.id);
                const cableData = cable.cableData;

                cableDiv.innerHTML = `
                    <div class="cable-name">
                        <span class="cable-color-indicator" style="background-color: ${actualCableColor}"></span>
                        ${cable.properties.name}
                    </div>
                    <div class="cable-details">
                        ${cableData.length ? `Length: ${cableData.length}` : ''}
                        ${cableData.rfs ? ` • RFS: ${cableData.rfs}` : ''}
                        ${cableData.owners ? `<br>Owners: ${cableData.owners}` : ''}
                    </div>
                    ${formatLandingPoints(cableData.landing_points)}
                `;

                // Add enhanced click handler with visual feedback
                cableDiv.addEventListener('click', () => {
                    // Add visual feedback to the clicked result
                    addClickFeedback(cableDiv);
                    centerMapOnCable(cable);
                    selectIndividualCable(cable.properties.id, cable);
                });
                cableResults.appendChild(cableDiv);
            });
        }

        function getActualCableColor(cableId) {
            let actualColor = '#3498db';

            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties.id === cableId) {
                    if (originalCableStyles.has(cableId)) {
                        actualColor = originalCableStyles.get(cableId).color;
                    } else if (layer.options.color) {
                        actualColor = layer.options.color;
                    } else if (layer.feature.properties.color) {
                        actualColor = layer.feature.properties.color;
                    }

                    if (!actualColor || actualColor === 'undefined') {
                        actualColor = '#3498db';
                    }
                }
            });

            return actualColor;
        }

        function validateCableIdentification() {
            let identifiedCables = 0;
            let totalCables = 0;

            cableLayer.eachLayer(layer => {
                totalCables++;
                if (layer.feature && layer.feature.properties.id) {
                    identifiedCables++;
                } else {
                    console.warn('Cable layer without proper identification:', layer);
                }
            });

            return identifiedCables === totalCables;
        }

        function addClickFeedback(element) {
            // Add a brief visual feedback when cable result is clicked
            element.style.transform = 'scale(0.98)';
            element.style.backgroundColor = '#e8f4fd';
            element.style.borderColor = '#3498db';

            setTimeout(() => {
                element.style.transform = 'scale(1)';
                element.style.backgroundColor = 'white';
                element.style.borderColor = '#e8e8e8';
            }, 200);
        }

        function centerMapOnCable(cable) {
            if (cable.geometry && cable.geometry.coordinates) {
                // Calculate bounds of the cable
                const coords = cable.geometry.coordinates;
                let bounds = L.latLngBounds();

                function addCoordinatesToBounds(coordinates) {
                    if (Array.isArray(coordinates[0])) {
                        if (Array.isArray(coordinates[0][0])) {
                            // MultiLineString
                            coordinates.forEach(lineString => {
                                lineString.forEach(coord => {
                                    bounds.extend([coord[1], coord[0]]);
                                });
                            });
                        } else {
                            // LineString
                            coordinates.forEach(coord => {
                                bounds.extend([coord[1], coord[0]]);
                            });
                        }
                    }
                }
                 addCoordinatesToBounds(coords);

                if (bounds.isValid()) {
                    // Enhanced map centering with better padding and zoom control
                    map.fitBounds(bounds, {
                        padding: [30, 30],
                        maxZoom: 6
                    });
                }
            }
        }

        function showCableNameLabel(cable) {
            clearCableNameLabel();
            if (!cable || !cable.properties || !cable.properties.name) {
                return;
            }

            // Find the cable layer to get its geometry
            let cableLayer = null;

            // Search through all cable layers to find the matching one
            map.eachLayer(layer => {
                if (layer.feature && layer.feature.properties &&
                    layer.feature.properties.id === cable.properties.id) {
                    cableLayer = layer;
                }
            });

            if (!cableLayer) {
                return;
            }

            // Get a point on the cable line for label placement
            let labelPosition = null;

            if (cableLayer.feature.geometry.type === 'LineString') {
                // Get coordinates from the cable geometry
                const coordinates = cableLayer.feature.geometry.coordinates;
                if (coordinates && coordinates.length > 0) {
                    // Use the middle point of the cable line
                    const middleIndex = Math.floor(coordinates.length / 2);
                    const coord = coordinates[middleIndex];
                    labelPosition = L.latLng(coord[1], coord[0]); // Note: GeoJSON is [lng, lat]
                }
            } else if (cableLayer.feature.geometry.type === 'MultiLineString') {
                // For MultiLineString, use the first line's middle point
                const firstLine = cableLayer.feature.geometry.coordinates[0];
                if (firstLine && firstLine.length > 0) {
                    const middleIndex = Math.floor(firstLine.length / 2);
                    const coord = firstLine[middleIndex];
                    labelPosition = L.latLng(coord[1], coord[0]);
                }
            }

            // Fallback to bounds center if geometry method fails
            if (!labelPosition) {
                const bounds = cableLayer.getBounds();
                if (bounds && bounds.isValid()) {
                    labelPosition = bounds.getCenter();
                } else {
                    console.log('Could not determine label position for cable');
                    return;
                }
            }

            // Create popup content with same styling as  cable popups
            const popupContent = `
                <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                    <h4 style="margin: 0; color: #2c3e50; font-size: 14px;">${cable.properties.name}</h4>
                </div>
            `;

            // Create and show the popup directly on the cable line
            currentCableNameLabel = L.popup({
                closeButton: true,
                autoClose: false,
                closeOnClick: false,
                className: 'cable-name-popup'
            })
            .setLatLng(labelPosition)
            .setContent(popupContent)
            .openOn(map);

            // Note: Removed auto-close timeout to keep tooltip persistent until user action
        }

        function clearCableNameLabel() {
            if (currentCableNameLabel) {
                map.closePopup(currentCableNameLabel);
                currentCableNameLabel = null;
            }
        }

        function highlightSpecificCable(cableId) {

            // Find the cable data for the name label
            let selectedCable = null;
            allCables.forEach(cable => {
                if (cable.properties && cable.properties.id === cableId) {
                    selectedCable = cable;
                }
            });

            if (selectedCable) {
                showCableNameLabel(selectedCable);
            }

            // Find and highlight the specific cable with enhanced visual effects
            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties.id === cableId) {
                    const originalStyle = originalCableStyles.get(cableId);

                    // Create a pulsing highlight effect
                    const pulseHighlight = () => {
                        layer.setStyle({
                            color: originalStyle.color,
                            opacity: 1.0,
                            weight: 8,
                            dashArray: null
                        });

                        // Add a subtle glow effect by creating a temporary shadow layer
                        setTimeout(() => {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 0.9,
                                weight: 7
                            });
                        }, 300);

                        setTimeout(() => {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 1.0,
                                weight: 8
                            });
                        }, 600);
                    };

                    // Start the pulse effect
                    pulseHighlight();
                    // Show popup with cable info if available
                    if (layer.getPopup()) {
                        // Find a good position for the popup (center of cable bounds)
                        const bounds = layer.getBounds();
                        if (bounds && bounds.isValid()) {
                            const center = bounds.getCenter();
                            layer.openPopup(center);
                        } else {
                            layer.openPopup();
                        }
                    }

                    // Reset to search result state after 4 seconds (but keep popup open)
                    setTimeout(() => {
                        if (isSearchActive) {
                            // Return to search result highlighting
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 1.0,
                                weight: 3.5
                            });
                        } else {
                            // If search was cleared, return to original state
                            layer.setStyle(originalStyle);
                        }

                    }, 4000);
                    layer.bringToFront();
                }
            });
        }

        let highlightedLandingPointIds = new Set();
        function highlightLandingPointsForCables(connectingCables, searchContext = null) {
            highlightedLandingPointIds.clear();

            connectingCables.forEach(cable => {
                if (cable.cableData && cable.cableData.landing_points) {
                    if (cable.pathType === 'multi-hop' && cable.pathInfo) {
                        // 🎯 MULTI-HOP: Show only landing points used in the specific segment
                        const segmentFrom = cable.pathInfo.segmentFrom;
                        const segmentTo = cable.pathInfo.segmentTo;

                        console.log(`🔗 Multi-hop segment: ${segmentFrom} ➜ ${segmentTo} via ${cable.properties.name}`);

                        // Find landing points in the segment countries
                        cable.cableData.landing_points.forEach(lp => {
                            if (lp.id && lp.country && (lp.country === segmentFrom || lp.country === segmentTo)) {
                                highlightedLandingPointIds.add(lp.id);
                                console.log(`  ✅ Added landing point: ${lp.name} (${lp.country})`);
                            }
                        });
                    } else if (cable.pathType === 'direct') {
                        // 🎯 DIRECT: Show only origin and destination landing points
                        if (searchContext && searchContext.fromCountry && searchContext.toCountry) {
                            console.log(`🔗 Direct connection: ${searchContext.fromCountry} ➜ ${searchContext.toCountry} via ${cable.properties.name}`);

                            cable.cableData.landing_points.forEach(lp => {
                                if (lp.id && lp.country &&
                                    (lp.country === searchContext.fromCountry || lp.country === searchContext.toCountry)) {
                                    highlightedLandingPointIds.add(lp.id);
                                    console.log(`  ✅ Added landing point: ${lp.name} (${lp.country})`);
                                }
                            });
                        } else {
                            // Fallback: show all landing points if no search context
                            cable.cableData.landing_points.forEach(lp => {
                                if (lp.id) highlightedLandingPointIds.add(lp.id);
                            });
                        }
                    } else {
                        // 🎯 FALLBACK: Show all landing points (backward compatibility)
                        cable.cableData.landing_points.forEach(lp => {
                            if (lp.id) highlightedLandingPointIds.add(lp.id);
                        });
                    }
                }
            });

            console.log(`🎯 Filtering landing points for search results: ${highlightedLandingPointIds.size} relevant points (path-specific filtering applied)`);

            // Apply the same filtering logic as individual cable clicks
            allLandingPointLayers.forEach((layer, landingPointId) => {
                if (highlightedLandingPointIds.has(landingPointId)) {
                    // Show relevant landing points in GREEN (search result filtering)
                    layer.setStyle({
                        fillColor: '#00FF00',   // GREEN - search result filtering
                        radius: 3,              // 🎯 CONSISTENT SIZE: Same as default (3px)
                        color: '#FFFFFF',       // White border for better contrast
                        weight: 1,              // Standard border weight
                        opacity: 1,             // Full opacity for filtered state
                        fillOpacity: 0.8        // Slightly transparent
                    });
                    // Ensure the layer is visible on the map
                    if (!landingPointLayer.hasLayer(layer)) {
                        landingPointLayer.addLayer(layer);
                    }
                    layer.bringToFront();
                } else {
                    // Hide irrelevant landing points (same as cable click behavior)
                    if (landingPointLayer.hasLayer(layer)) {
                        landingPointLayer.removeLayer(layer);
                    }
                }
            });

            // Set the landing point filter as active (consistent with cable click state)
            isLandingPointFilterActive = true;
            console.log('🔍 Search result landing point filtering applied - showing only relevant points in GREEN');
        }
        function resetLandingPointHighlights() {
            highlightedLandingPointIds.clear();

            // Restore all landing points to the map and reset their styling
            allLandingPointLayers.forEach((layer, landingPointId) => {
                // Reset to consistent standard styling
                layer.setStyle({
                    fillColor: '#FF0000',   // Standard red
                    radius: 3,              // 🎯 CONSISTENT SIZE: Same as initial load (3px)
                    color: '#FFFFFF',       // 🎯 WHITE BORDER: Match initial load
                    weight: 1,              // 🎯 CONSISTENT BORDER: Match initial load weight
                    opacity: 1,             // 🎯 FULL OPACITY: Match initial load for consistency
                    fillOpacity: 0.8        // 🎯 CONSISTENT TRANSPARENCY: Match initial load
                });

                // Ensure all landing points are visible on the map
                if (!landingPointLayer.hasLayer(layer)) {
                    landingPointLayer.addLayer(layer);
                }
                // 🎯 LAYER ORDERING: Ensure landing points stay on top
                layer.bringToFront();
            });

            // Clear the landing point filter state
            isLandingPointFilterActive = false;
            console.log('🔄 Landing point highlights reset - all points restored to RED and visible');
        }

        let filteredAfricaEuropeGeoJSON = null;

        // When you first filter and display Africa/Europe cables
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                // Show ALL cables worldwide (no regional filtering)
                filteredAfricaEuropeGeoJSON = data;
                filteredCables = data.features;

                // Create cables with professional color scheme and interactive labeling
                L.geoJSON(filteredAfricaEuropeGeoJSON, {
                    style: function(feature) {
                        const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                        return getCableStyle(feature, cableIndex);
                    },
                    onEachFeature: function(feature, layer) {
                        const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id);

                        layer.on({
                            mouseover: function(e) {
                                // Only apply hover effects if individual cable selection is not active
                                if (!isIndividualCableSelected) {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 4,
                                        opacity: 1
                                    });
                                    info.update(feature.properties);
                                }
                            },
                            mouseout: function(e) {
                                // Only restore hover effects if individual cable selection is not active
                                if (!isIndividualCableSelected) {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 2.5,
                                        opacity: 0.85
                                    });
                                    info.update();
                                }
                            },
                            click: function(e) {
                                selectIndividualCable(feature.properties.id, feature);
                                L.DomEvent.stopPropagation(e);
                            }
                        });

                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';

                        if (feature.properties.rfs) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                        }
                        if (feature.properties.length) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                        }
                        if (feature.properties.owners) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                        }

                        popupContent += '</div>';

                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
            });

        // Function to restore the Africa/Europe filtered cables
        function restoreAfricaEuropeCables() {
            cableLayer.clearLayers();
            if (!filteredAfricaEuropeGeoJSON) return;
            // Re-render using wrapped cables so they repeat across the date line
            const wrappedCables2 = createWrappedGeoJSON(filteredAfricaEuropeGeoJSON);
            cableLayer.clearLayers();
            L.geoJSON(wrappedCables2, {
                style: function(feature) { return getCableStyle(feature); },
                onEachFeature: function(feature, layer) {
                    layer.on({
                        mouseover: function(e) {
                            if (!isIndividualCableSelected) {
                                const l = e.target;
                                l.setStyle({ weight: 4, opacity: 1 });
                                info.update(feature.properties);
                            }
                        },
                        mouseout: function(e) {
                            if (!isIndividualCableSelected) {
                                const l = e.target;
                                l.setStyle({ weight: 2.5, opacity: 0.85 });
                                info.update();
                            }
                        },
                        click: function(e) {
                            selectIndividualCable(feature.properties.id, feature);
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    if (feature.properties.rfs) popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    if (feature.properties.length) popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    if (feature.properties.owners) popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }

        // Enhanced search functionality
        async function performSearch() {
            const fromInput = document.getElementById('fromCountry').value;
            const toInput = document.getElementById('toCountry').value;
            const searchType = document.getElementById('searchType').value;

            // Convert search inputs to countries
            const fromCountry = getCountryFromSearchInput(fromInput);
            const toCountry = getCountryFromSearchInput(toInput);

            // Validate that both inputs could be converted to valid countries
            if (!fromCountry || !toCountry) {
                const resultsContainer = document.getElementById('cableResults');
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">❌</div>
                        <div class="no-results-text">Invalid search input</div>
                        <div class="no-results-suggestion">Please select valid countries or landing points from the dropdown.</div>
                    </div>
                `;
                return;
            }



            // Show loading state
            const searchBtn = document.getElementById('searchBtn');
            const originalText = searchBtn.textContent;
            searchBtn.textContent = 'Searching...';
            searchBtn.disabled = true;

            try {
                // Determine if we're searching by specific landing points or countries
                const fromLandingPoint = availableLandingPoints.has(fromInput) ? fromInput : null;
                const toLandingPoint = availableLandingPoints.has(toInput) ? toInput : null;

                // Find cables connecting these locations based on search type
                const rawSearchResults = await findConnectingCables(
                    fromCountry, toCountry, searchType, fromLandingPoint, toLandingPoint
                );

                // Filter search results to only include cables visible on the map
                const searchResults = filterSearchResultsToVisibleCables(rawSearchResults);

                let allCables = [];
                if (searchType === 'both') {
                    // Combine direct and multi-hop results
                    allCables = [...(searchResults.direct || []), ...(searchResults.multiHop || [])];
                } else {
                    allCables = searchResults || [];
                }
                // Verify all search results are visible on the map
                const invisibleCables = allCables.filter(cable => !isCableVisibleOnMap(cable.properties.id));
                if (invisibleCables.length > 0) {
                       invisibleCables.map(c => c.properties.name);
                }

                showOnlyCables(allCables);

                // Reset landing point highlights before applying new ones (fixes state management issue)
                resetLandingPointHighlights();

                // 🎯 STORE SEARCH CONTEXT: Save context and cable data for smart filtering
                currentSearchContext = {
                    fromCountry: fromCountry,
                    toCountry: toCountry,
                    fromLandingPoint: fromLandingPoint,
                    toLandingPoint: toLandingPoint
                };

                // Store cable data with path info for individual cable selection
                searchResultCables.clear();
                allCables.forEach(cable => {
                    searchResultCables.set(cable.properties.id, cable);
                });

                console.log(`🎯 Stored search context and ${searchResultCables.size} cable data entries for smart filtering`);
                console.log(`🔍 Search context:`, currentSearchContext);
                console.log(`🔍 Sample stored cables:`, Array.from(searchResultCables.entries()).slice(0, 2).map(([id, cable]) => ({
                    id,
                    pathType: cable.pathType,
                    pathInfo: cable.pathInfo
                })));

                highlightLandingPointsForCables(allCables, currentSearchContext);

                // Display results in sidebar with enhanced multi-hop support
                if (searchType === 'both') {
                    displayEnhancedSearchResults(searchResults, fromCountry, toCountry);
                } else {
                    displaySearchResults(allCables, fromCountry, toCountry, searchType);
                }

                isSearchActive = true;

            } catch (error) {
                document.getElementById('cableResults').innerHTML = '<div class="no-results">Search failed. Please try again.</div>';
            } finally {
                // Restore search button
                searchBtn.textContent = originalText;
                searchBtn.disabled = false;
            }
        }

        // Enhanced display functions
        function displaySearchResults(cables, fromCountry, toCountry, searchType = 'direct') {
            const resultsContainer = document.getElementById('cableResults');

            if (cables.length === 0) {
                const searchTypeText = searchType === 'multi-hop' ? 'multi-hop routes' : 'direct connections';
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">➤</div>
                        <div class="no-results-text">No ${searchTypeText} found between ${fromCountry} and ${toCountry}</div>
                        <div class="no-results-suggestion">Try searching for different countries or a different search type.</div>
                    </div>
                `;
                return;
            }

            // Group cables by path type for multi-hop results
            const directCables = cables.filter(c => c.pathType === 'direct' || !c.pathType);
            const multiHopCables = cables.filter(c => c.pathType === 'multi-hop');

            let html = `
                <div class="search-results-header">
                    <div class="results-title">
                        <span class="results-icon">🔗</span>
                        Found ${cables.length} connection${cables.length !== 1 ? 's' : ''}
                    </div>
                    <div class="results-route">
                        <span class="country-tag">${fromCountry}</span>
                        <span class="route-arrow">➜</span>
                        <span class="country-tag">${toCountry}</span>
                    </div>
                </div>
            `;

            // Display direct connections
            if (directCables.length > 0) {
                html += `
                    <div class="path-section">
                        <div class="path-header">
                            <span class="path-type-badge path-type-direct">Direct</span>
                            ${directCables.length} Direct Connection${directCables.length !== 1 ? 's' : ''}
                        </div>
                        <div class="cables-list">
                `;

                directCables.forEach((cable, index) => {
                    html += generateCableHTML(cable, index, 'direct');
                });

                html += '</div></div>';
            }

            // Display multi-hop connections
            if (multiHopCables.length > 0) {
                // Group by path
                const pathGroups = new Map();
                multiHopCables.forEach(cable => {
                    const pathKey = cable.pathInfo.fullPath.join('➜');
                    if (!pathGroups.has(pathKey)) {
                        pathGroups.set(pathKey, []);
                    }
                    pathGroups.get(pathKey).push(cable);
                });

                pathGroups.forEach((pathCables, pathKey) => {
                    const fullPath = pathCables[0].pathInfo.fullPath;

                    html += `
                        <div class="path-section">
                            <div class="path-header">
                                <span class="path-type-badge path-type-multi-hop">Multi-hop</span>
                                Route via ${fullPath.length - 2} intermediate countr${fullPath.length - 2 !== 1 ? 'ies' : 'y'}
                            </div>
                            <div class="path-route">
                                <div class="path-route-label">Route:</div>
                                <div class="path-countries">
                                    ${fullPath.map((country, idx) => `
                                        <span class="path-country ${(idx === 0 || idx === fullPath.length - 1) ? 'highlight' : ''}">${country}</span>
                                        ${idx < fullPath.length - 1 ? '<span class="path-arrow" style="color: #3498db; font-weight: bold;">➜</span>' : ''}
                                    `).join('')}
                                </div>
                            </div>
                            <div class="path-cables">
                    `;

                    // Group cables by segment to avoid duplicate segment headers
                    const segmentGroups = new Map();
                    pathCables.forEach((cable) => {
                        const segmentKey = `${cable.pathInfo.segmentIndex}-${cable.pathInfo.segmentFrom}-${cable.pathInfo.segmentTo}`;
                        if (!segmentGroups.has(segmentKey)) {
                            segmentGroups.set(segmentKey, []);
                        }
                        segmentGroups.get(segmentKey).push(cable);
                    });

                    segmentGroups.forEach((segmentCables, segmentKey) => {
                        const segmentInfo = segmentCables[0].pathInfo;
                        html += `
                            <div class="path-segment">
                                <div class="segment-header">
                                    Segment ${segmentInfo.segmentIndex + 1}: ${segmentInfo.segmentFrom} ➜ ${segmentInfo.segmentTo}
                                </div>
                                <div class="segment-cables">
                        `;

                        segmentCables.forEach((cable, index) => {
                            html += generateCableHTML(cable, index, 'multi-hop');
                        });

                        html += `
                                </div>
                            </div>
                        `;
                    });
                    html += '</div></div>';
                });
            }
            resultsContainer.innerHTML = html;
        }

        function displayEnhancedSearchResults(searchResults, fromCountry, toCountry) {
            const resultsContainer = document.getElementById('cableResults');
            const directCables = searchResults.direct || [];
            const multiHopCables = searchResults.multiHop || [];
            const totalConnections = directCables.length + (multiHopCables.length > 0 ? 1 : 0); // Count multi-hop as one route

            if (totalConnections === 0) {
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">🔎</div>
                        <div class="no-results-text">No connections found between ${fromCountry} and ${toCountry}</div>
                        <div class="no-results-suggestion">These countries may not be connected via submarine cables.</div>
                    </div>
                `;
                return;
            }

            // Use a global index counter to ensure unique colors across all cables
            let globalCableIndex = 0;

            let html = `
                <div class="search-results-header">
                    <div class="results-title">
                        <span class="results-icon">🔗</span>
                        Found ${totalConnections} connection type${totalConnections !== 1 ? 's' : ''}
                    </div>
                    <div class="results-route">
                        <span class="country-tag">${fromCountry}</span>
                        <span class="route-arrow">➜</span>
                        <span class="country-tag">${toCountry}</span>
                    </div>
                </div>
            `;
            // Display direct connections
            if (directCables.length > 0) {
                html += `
                    <div class="path-section">
                        <div class="path-header">
                            <span class="path-type-badge path-type-direct">Direct</span>
                            ${directCables.length} Direct Connection${directCables.length !== 1 ? 's' : ''}
                        </div>
                        <div class="cables-list">
                `;

                directCables.forEach((cable) => {
                    html += generateCableHTML(cable, globalCableIndex++, 'direct');
                });

                html += '</div></div>';
            }

            // Display multi-hop connections
            if (multiHopCables.length > 0) {
                // Group by path
                const pathGroups = new Map();
                multiHopCables.forEach(cable => {
                    const pathKey = cable.pathInfo.fullPath.join('➜');
                    if (!pathGroups.has(pathKey)) {
                        pathGroups.set(pathKey, []);
                    }
                    pathGroups.get(pathKey).push(cable);
                });

                pathGroups.forEach((pathCables, pathKey) => {
                    const fullPath = pathCables[0].pathInfo.fullPath;

                    html += `
                        <div class="path-section">
                            <div class="path-header">
                                <span class="path-type-badge path-type-multi-hop">Multi-hop</span>
                                Route via ${fullPath.length - 2} intermediate countr${fullPath.length - 2 !== 1 ? 'ies' : 'y'}
                            </div>
                            <div class="path-route">
                                <div class="path-route-label">Route:</div>
                                <div class="path-countries">
                                    ${fullPath.map((country, idx) => `
                                        <span class="path-country ${(idx === 0 || idx === fullPath.length - 1) ? 'highlight' : ''}">${country}</span>
                                        ${idx < fullPath.length - 1 ? '<span class="path-arrow">→</span>' : ''}
                                    `).join('')}
                                </div>
                            </div>
                            <div class="path-cables">
                    `;

                    // Group cables by segment to avoid duplicate segment headers
                    const segmentGroups = new Map();
                    pathCables.forEach((cable) => {
                        const segmentKey = `${cable.pathInfo.segmentIndex}-${cable.pathInfo.segmentFrom}-${cable.pathInfo.segmentTo}`;
                        if (!segmentGroups.has(segmentKey)) {
                            segmentGroups.set(segmentKey, []);
                        }
                        segmentGroups.get(segmentKey).push(cable);
                    });

                    segmentGroups.forEach((segmentCables, segmentKey) => {
                        const segmentInfo = segmentCables[0].pathInfo;
                        html += `
                            <div class="path-segment">
                                <div class="segment-header">
                                    Segment ${segmentInfo.segmentIndex + 1}: ${segmentInfo.segmentFrom} ➜ ${segmentInfo.segmentTo}
                                </div>
                                <div class="segment-cables">
                        `;

                        segmentCables.forEach((cable) => {
                            html += generateCableHTML(cable, globalCableIndex++, 'multi-hop');
                        });

                        html += `
                                </div>
                            </div>
                        `;
                    });

                    html += '</div></div>';
                });
            }
            resultsContainer.innerHTML = html;
        }

        function generateCableHTML(cable, index, pathType) {
            const cableData = cable.cableData || {};
            const cableName = cableData.name || cable.properties.name || 'Unknown Cable';
            const cableLength = cableData.length || 'Unknown';
            const rfsYear = cableData.rfs_year || 'Unknown';
            const owners = cableData.owners || 'Unknown';

            return `
                <div class="cable-result">
                    <div class="cable-name" onclick="toggleCableDetails(this, '${cable.properties.id}', ${JSON.stringify(cable).replace(/"/g, '&quot;')})">
                        <span class="cable-color-indicator" style="background-color: ${getProfessionalCableColor(cable.properties.id)}"></span>
                        ${cableName}
                    </div>
                    <div class="cable-details">
                        ${cableLength !== 'Unknown' ? `<div>Length: ${cableLength}</div>` : ''}
                        ${rfsYear !== 'Unknown' ? `<div>RFS: ${rfsYear}</div>` : ''}
                        ${owners !== 'Unknown' ? `<div>Owners: ${owners}</div>` : ''}
                    </div>
                    ${formatLandingPoints(cableData.landing_points)}
                </div>
            `;
        }

        async function loadCableData(cableId) {
            try {
                const response = await fetch(`../cable/${cableId}.json`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                return null;
            }
        }
        // Global scope: accept all countries
        function isEuropeanOrAfricanCountry(country) {
            return true;
        }

        // Build country connectivity graph from all cable data
        async function buildCountryGraph() {
            countryGraph.clear();
            cableConnections.clear();
            console.log('Building global country graph');

            // Process only filtered cables visible on map to build the graph
            for (const cable of filteredCables) {
                try {
                    // Load individual cable data
                    const cableData = await loadCableData(cable.properties.id);
                    if (!cableData || !cableData.landing_points) continue;

                    const landingPoints = cableData.landing_points;
                    const allCountries = [...new Set(landingPoints.map(lp => lp.country))];

                    // Filter to only include European and African countries
                    const countries = allCountries.filter(country => isEuropeanOrAfricanCountry(country));

                    if (countries.length < 2) {
                        // Skip cables that don't connect at least 2 European/African countries
                        continue;
                    }

                    if (countries.includes('Cyprus') || countries.includes('Finland')) {
                        console.log(`Cable ${cable.properties.id} connects (filtered):`, countries);
                    }

                    // Add bidirectional connections between all countries in this cable
                    for (let i = 0; i < countries.length; i++) {
                        for (let j = i + 1; j < countries.length; j++) {
                            const country1 = countries[i];
                            const country2 = countries[j];

                            // Add to adjacency list
                            if (!countryGraph.has(country1)) countryGraph.set(country1, new Set());
                            if (!countryGraph.has(country2)) countryGraph.set(country2, new Set());

                            countryGraph.get(country1).add(country2);
                            countryGraph.get(country2).add(country1);

                            // Store cable connection info
                            const connectionKey1 = `${country1}➜${country2}`;
                            const connectionKey2 = `${country2}➜${country1}`;

                            if (!cableConnections.has(connectionKey1)) cableConnections.set(connectionKey1, []);
                            if (!cableConnections.has(connectionKey2)) cableConnections.set(connectionKey2, []);

                            const cableInfo = { ...cable, cableData };
                            cableConnections.get(connectionKey1).push(cableInfo);
                            cableConnections.get(connectionKey2).push(cableInfo);
                        }
                    }
                } catch (error) {
                    console.warn(`Failed to process cable ${cable.properties.id}:`, error);
                }
            }

            console.log(`Country graph built with ${countryGraph.size} European/African countries`);
            console.log('Countries in graph:', Array.from(countryGraph.keys()).sort());

            if (countryGraph.has('Cyprus')) {
                console.log('Cyprus connections:', Array.from(countryGraph.get('Cyprus')));
            }
            if (countryGraph.has('Finland')) {
                console.log('Finland connections:', Array.from(countryGraph.get('Finland')));
            }
        }
        // Find multi-hop path using BFS
        function findMultiHopPath(fromCountry, toCountry) {
            if (!countryGraph.has(fromCountry) || !countryGraph.has(toCountry)) {
                console.log(`Path finding failed: ${fromCountry} or ${toCountry} not in graph`);
                return null;
            }

            // Validate that both countries are European/African
            if (!isEuropeanOrAfricanCountry(fromCountry) || !isEuropeanOrAfricanCountry(toCountry)) {
                console.log(`Path finding failed: ${fromCountry} or ${toCountry} not in Europe/Africa scope`);
                return null;
            }

            const queue = [[fromCountry]];
            const visited = new Set([fromCountry]);

            while (queue.length > 0) {
                const path = queue.shift();
                const currentCountry = path[path.length - 1];

                if (currentCountry === toCountry) {
                    console.log(`Found path: ${path.join(' ➜ ')}`);
                    return path;
                }

                const neighbors = countryGraph.get(currentCountry) || new Set();
                for (const neighbor of neighbors) {
                    // Only consider neighbors that are European/African countries
                    if (!visited.has(neighbor) && isEuropeanOrAfricanCountry(neighbor)) {
                        visited.add(neighbor);
                        queue.push([...path, neighbor]);
                    }
                }
            }

            console.log(`No path found between ${fromCountry} and ${toCountry}`);
            return null;
        }
        // Get cables for a specific segment of the path
        function getCablesForSegment(fromCountry, toCountry) {
            const connectionKey = `${fromCountry}➜${toCountry}`;
            return cableConnections.get(connectionKey) || [];
        }

        // Enhanced findConnectingCables function with multi-hop support and landing point specificity
        async function findConnectingCables(fromCountry, toCountry, searchType = 'direct', fromLandingPoint = null, toLandingPoint = null) {
            if (searchType === 'direct') {
                return await findDirectConnections(fromCountry, toCountry, fromLandingPoint, toLandingPoint);
            } else if (searchType === 'multi-hop') {
                return await findMultiHopConnections(fromCountry, toCountry, fromLandingPoint, toLandingPoint);
            } else if (searchType === 'both') {
                const direct = await findDirectConnections(fromCountry, toCountry, fromLandingPoint, toLandingPoint);
                const multiHop = await findMultiHopConnections(fromCountry, toCountry, fromLandingPoint, toLandingPoint);
                return { direct, multiHop };
            }

            return [];
        }

        // Find direct connections with optional landing point specificity
        async function findDirectConnections(fromCountry, toCountry, fromLandingPoint = null, toLandingPoint = null) {
            const connectingCables = [];

            for (const cable of filteredCables) {
                try {
                    const cableData = await loadCableData(cable.properties.id);
                    if (!cableData || !cableData.landing_points) continue;

                    // Check if cable connects the specified locations
                    let hasFromLocation = false;
                    let hasToLocation = false;

                    for (const landingPoint of cableData.landing_points) {
                        // Check "from" location
                        if (fromLandingPoint) {
                            // If specific landing point is specified, match exactly
                            if (landingPoint.name === fromLandingPoint) {
                                hasFromLocation = true;
                            }
                        } else {
                            // If only country is specified, match any landing point in that country
                            if (landingPoint.country === fromCountry) {
                                hasFromLocation = true;
                            }
                        }

                        // Check "to" location
                        if (toLandingPoint) {
                            // If specific landing point is specified, match exactly
                            if (landingPoint.name === toLandingPoint) {
                                hasToLocation = true;
                            }
                        } else {
                            // If only country is specified, match any landing point in that country
                            if (landingPoint.country === toCountry) {
                                hasToLocation = true;
                            }
                        }
                    }

                    // Add cable if it connects both specified locations
                    if (hasFromLocation && hasToLocation) {
                        connectingCables.push({
                            ...cable,
                            cableData,
                            pathType: 'direct'
                        });
                    }
                } catch (error) {
                    console.warn(`Failed to load cable data for ${cable.properties.id}:`, error);
                }
            }
            return connectingCables;
        }
        // Enhanced multi-hop connections with landing point specificity
        async function findMultiHopConnections(fromCountry, toCountry, fromLandingPoint = null, toLandingPoint = null) {
            if (countryGraph.size === 0) {
                await buildCountryGraph();
            }

            console.log('Multi-hop search:', { fromCountry, toCountry, fromLandingPoint, toLandingPoint });

            const path = findMultiHopPath(fromCountry, toCountry);
            console.log(`Multi-hop path search: ${fromCountry} → ${toCountry}`);
            console.log('Path result:', path);

            if (!path || path.length < 3) {
                console.log('No multi-hop path found or path too short');

                // Try reverse direction to see if it's a directionality issue
                console.log(`Trying reverse direction: ${toCountry} → ${fromCountry}`);
                const reversePath = findMultiHopPath(toCountry, fromCountry);
                console.log('Reverse path result:', reversePath);

                if (reversePath && reversePath.length >= 3) {
                    console.log('⚠️ ASYMMETRY DETECTED: Reverse direction has path but forward direction does not');
                    console.log('This suggests a directionality issue in the cable connection mapping');
                }

                return [];
            }

            console.log('Multi-hop path found:', path);

            // FIRST: Validate all segments before processing any
            console.log('\n=== VALIDATING ALL SEGMENTS FIRST ===');
            const segmentValidation = [];

            for (let i = 0; i < path.length - 1; i++) {
                const segmentFrom = path[i];
                const segmentTo = path[i + 1];
                console.log(`\n--- Validating segment ${i + 1}/${path.length - 1}: ${segmentFrom} → ${segmentTo} ---`);

                let segmentCables = getCablesForSegment(segmentFrom, segmentTo);
                console.log(`Found ${segmentCables.length} cables for segment ${segmentFrom} → ${segmentTo}:`,
                    segmentCables.map(c => c.properties.id));

                // Filter first segment to match specific FROM landing point
                if (i === 0 && fromLandingPoint) {
                    console.log(`Validating first segment (${segmentFrom} → ${segmentTo}) for FROM landing point:`, fromLandingPoint);
                    segmentCables = await filterCablesForLandingPoint(segmentCables, fromLandingPoint, 'from');
                    console.log(`After FROM filtering: ${segmentCables.length} cables remain`);
                }

                // Filter last segment to match specific TO landing point
                if (i === path.length - 2 && toLandingPoint) {
                    console.log(`Validating last segment (${segmentFrom} → ${segmentTo}) for TO landing point:`, toLandingPoint);
                    segmentCables = await filterCablesForLandingPoint(segmentCables, toLandingPoint, 'to');
                    console.log(`After TO filtering: ${segmentCables.length} cables remain`);
                }

                segmentValidation.push({
                    from: segmentFrom,
                    to: segmentTo,
                    cables: segmentCables,
                    valid: segmentCables.length > 0,
                    segmentIndex: i
                });

                console.log(`Segment ${segmentFrom} → ${segmentTo}: ${segmentCables.length > 0 ? '✅ VALID' : '❌ INVALID'}`);
            }

            // Check if ALL segments are valid
            const invalidSegments = segmentValidation.filter(seg => !seg.valid);
            if (invalidSegments.length > 0) {
                console.log('\n❌ MULTI-HOP PATH INVALID - Missing segments:');
                invalidSegments.forEach(seg => {
                    console.log(`   ${seg.from} → ${seg.to}: No valid cables found`);
                });

                // Try to find alternative paths if the current path fails due to landing point constraints
                if ((fromLandingPoint || toLandingPoint) && invalidSegments.length > 0) {
                    console.log('\n🔄 TRYING ALTERNATIVE PATHS...');

                    // Try finding paths that work with the specific landing points
                    const alternativePaths = await findAlternativeMultiHopPaths(fromCountry, toCountry, fromLandingPoint, toLandingPoint);
                    if (alternativePaths.length > 0) {
                        console.log('✅ Found alternative multi-hop path!');
                        return alternativePaths;
                    }
                }

                console.log('Returning empty result - no valid multi-hop path exists');
                return [];
            }

            console.log('\n✅ ALL SEGMENTS VALID - Processing multi-hop path');

            // Now process all segments since we know they're all valid
            const pathSegments = [];
            for (const segment of segmentValidation) {
                for (const cable of segment.cables) {
                    pathSegments.push({
                        ...cable,
                        pathType: 'multi-hop',
                        pathInfo: {
                            fullPath: path,
                            segmentIndex: segment.segmentIndex,
                            segmentFrom: segment.from,
                            segmentTo: segment.to,
                            isFirstSegment: segment.segmentIndex === 0,
                            isLastSegment: segment.segmentIndex === path.length - 2
                        }
                    });
                }
            }
            return pathSegments;
        }

        // Find alternative multi-hop paths when primary path fails due to landing point constraints
        async function findAlternativeMultiHopPaths(fromCountry, toCountry, fromLandingPoint, toLandingPoint) {
            console.log('Searching for alternative paths...');

            // Strategy: Find all possible intermediate countries and test different path combinations
            const allPossiblePaths = [];

            // Get all countries that connect to fromCountry
            const fromNeighbors = countryGraph.get(fromCountry) || new Set();
            // Get all countries that connect to toCountry
            const toNeighbors = countryGraph.get(toCountry) || new Set();

            console.log(`${fromCountry} connects to:`, Array.from(fromNeighbors));
            console.log(`${toCountry} connects to:`, Array.from(toNeighbors));

            // Try direct connections through each possible intermediate country
            for (const intermediate of fromNeighbors) {
                if (toNeighbors.has(intermediate) && intermediate !== fromCountry && intermediate !== toCountry) {
                    console.log(`Testing alternative path: ${fromCountry} → ${intermediate} → ${toCountry}`);

                    // Test this specific path
                    const testPath = [fromCountry, intermediate, toCountry];
                    const pathCables = await validateSpecificPath(testPath, fromLandingPoint, toLandingPoint);

                    if (pathCables.length > 0) {
                        console.log(`✅ Alternative path works: ${testPath.join(' → ')}`);
                        allPossiblePaths.push(...pathCables);
                    }
                }
            }

            return allPossiblePaths;
        }

        // Validate a specific path with landing point constraints
        async function validateSpecificPath(path, fromLandingPoint, toLandingPoint) {
            const pathSegments = [];

            for (let i = 0; i < path.length - 1; i++) {
                const segmentFrom = path[i];
                const segmentTo = path[i + 1];

                let segmentCables = getCablesForSegment(segmentFrom, segmentTo);

                // Apply landing point filters
                if (i === 0 && fromLandingPoint) {
                    segmentCables = await filterCablesForLandingPoint(segmentCables, fromLandingPoint, 'from');
                }
                if (i === path.length - 2 && toLandingPoint) {
                    segmentCables = await filterCablesForLandingPoint(segmentCables, toLandingPoint, 'to');
                }

                // If any segment has no cables, this path is invalid
                if (segmentCables.length === 0) {
                    return [];
                }

                // Add cables to path
                for (const cable of segmentCables) {
                    pathSegments.push({
                        ...cable,
                        pathType: 'multi-hop',
                        pathInfo: {
                            fullPath: path,
                            segmentIndex: i,
                            segmentFrom,
                            segmentTo,
                            isFirstSegment: i === 0,
                            isLastSegment: i === path.length - 2
                        }
                    });
                }
            }

            return pathSegments;
        }

        // Helper function to filter cables that connect to a specific landing point
        async function filterCablesForLandingPoint(cables, targetLandingPoint, direction) {
            const filteredCables = [];

            for (const cable of cables) {
                try {
                    const cableData = await loadCableData(cable.properties.id);
                    if (!cableData || !cableData.landing_points) continue;

                    // Check if this cable connects to the target landing point
                    const hasTargetLandingPoint = cableData.landing_points.some(lp =>
                        lp.name === targetLandingPoint
                    );

                    if (hasTargetLandingPoint) {
                        console.log(`Cable ${cable.properties.id} connects to ${direction} landing point:`, targetLandingPoint);
                        filteredCables.push(cable);
                    }
                } catch (error) {
                    console.warn(`Failed to load cable data for ${cable.properties.id}:`, error);
                }
            }

            return filteredCables;
        }
